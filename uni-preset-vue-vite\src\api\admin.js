/**
 * 管理员相关API
 */
import { get, post, put, del } from '@/utils/request';

/**
 * 获取所有患者列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 患者列表
 */
export const getPatientList = (params = {}) => {
  return get('/admin/patients', params);
};

/**
 * 获取所有医生列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 医生列表
 */
export const getDoctorList = (params = {}) => {
  return get('/admin/doctors', params);
};

/**
 * 添加医生账号
 * @param {Object} data - 医生信息
 * @returns {Promise} - 添加结果
 */
export const addDoctor = (data) => {
  return post('/admin/doctors', data);
};

/**
 * 修改用户账号信息
 * @param {string} userId - 用户ID
 * @param {Object} data - 用户信息
 * @returns {Promise} - 修改结果
 */
export const updateUser = (userId, data) => {
  if (!userId) {
    return Promise.reject(new Error('未提供用户ID'));
  }
  return put(`/admin/users/${userId}`, data);
};

/**
 * 删除用户账号
 * @param {string} userId - 用户ID
 * @returns {Promise} - 删除结果
 */
export const deleteUser = (userId) => {
  if (!userId) {
    return Promise.reject(new Error('未提供用户ID'));
  }
  return del(`/admin/users/${userId}`);
};

export default {
  getPatientList,
  getDoctorList,
  addDoctor,
  updateUser,
  deleteUser
}; 