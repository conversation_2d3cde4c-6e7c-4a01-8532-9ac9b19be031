<template>
  <view class="container">
    <view class="header">
      <view class="welcome-text">欢迎使用智慧口腔健康医疗</view>
      <view class="user-info">
        <text class="user-name">{{ userInfo.name ? `尊敬的${userInfo.name}` : '尊敬的用户' }}</text>
      </view>
    </view>
    
    <view class="options-container">
      <!-- 预约挂号选项 -->
      <view class="option-card" @tap="navigateToAppointment">
        <view class="option-content">
          <text class="option-title">预约挂号</text>
          <text class="option-desc">选择专科、医生和时间进行预约</text>
        </view>
        <view class="option-icon">
          <text class="icon">📅</text>
        </view>
      </view>
      
      <!-- 预约记录选项 -->
      <view class="option-card" @tap="navigateToRecords">
        <view class="option-content">
          <text class="option-title">预约记录</text>
          <text class="option-desc">查看历史预约和当前预约状态</text>
        </view>
        <view class="option-icon">
          <text class="icon">📋</text>
        </view>
      </view>
      
      <!-- 医生评分选项 -->
      <view class="option-card doctor-rating" @tap="navigateToDoctorRating">
        <view class="option-content">
          <text class="option-title">医生评分</text>
          <text class="option-desc">为您的就诊医生提供评价和反馈</text>
        </view>
        <view class="option-icon">
          <text class="icon">⭐</text>
        </view>
      </view>
      
      <!-- 个人信息选项 -->
      <view class="option-card personal-info" @tap="navigateToProfile">
        <view class="option-content">
          <text class="option-title">个人信息</text>
          <text class="option-desc">查看并管理您的个人信息和健康数据</text>
        </view>
        <view class="option-icon">
          <text class="icon">👤</text>
        </view>
      </view>
      
      <!-- 健康建议选项 -->
      <view class="option-card health-advice" @tap="navigateToProductRecommendation">
        <view class="option-content">
          <text class="option-title">健康建议</text>
          <text class="option-desc">根据口腔健康状况提供个性化护理建议</text>
        </view>
        <view class="option-icon">
          <text class="icon">💡</text>
        </view>
      </view>
    </view>
    
    <!-- 退出登录按钮 -->
    <view class="logout-container">
      <button class="logout-button" @tap="logout">退出登录</button>
    </view>
    
    <view class="footer">
      <text class="footer-text">智慧口腔 · 呵护您的健康</text>
    </view>
  </view>
</template>

<script>
export default {
  onBackPress() {
    uni.reLaunch({
      url: '/pages/login/index'
    });
    return true; // 返回true，表示已处理返回事件
  }
}
</script>

<script setup>
import { ref, onMounted, getCurrentInstance, onBeforeMount } from 'vue';
import { getUserInfo as getStoredUserInfo } from '@/utils/storage';

// 获取全局API
const { proxy } = getCurrentInstance();
const { $api } = proxy;

// 用户信息
const userInfo = ref({
  name: '',
  id: ''
});

// 获取用户信息
const fetchPatientInfo = async () => {
  try {
    // 显示加载
    uni.showLoading({
      title: '加载中...'
    });
    
    // 获取本地存储的用户信息（基本信息）
    const storedUserInfo = getStoredUserInfo();
    if (storedUserInfo) {
      userInfo.value.name = storedUserInfo.name || '';
      userInfo.value.id = storedUserInfo.userId || '';
    }
    
    // 从服务器获取完整的用户信息
    const result = await $api.patient.getPatientInfo();
    if (result && result.data) {
      userInfo.value = result.data;
    }
  } catch (error) {
    console.error('获取用户信息失败', error);
    uni.showToast({
      title: '获取用户信息失败',
      icon: 'none'
    });
  } finally {
    uni.hideLoading();
  }
};

// 页面加载时
onMounted(() => {
  fetchPatientInfo();
  
  // 设置页面返回处理函数，仅对用户中心页面有效
  const pages = getCurrentPages();
  const page = pages[pages.length - 1];
  if (page && page.$vm) {
    page.$vm.$options.onBackPress = () => {
      uni.reLaunch({
        url: '/pages/login/index'
      });
      return true; // 返回true，表示已处理返回事件
    };
  }
});

// 不再全局拦截返回按钮
// onBeforeMount(() => {
//   // 监听页面返回
//   uni.addInterceptor('navigateBack', {
//     invoke(e) {
//       // 阻止默认返回行为
//       uni.reLaunch({
//         url: '/pages/login/index'
//       });
//       return false; // 阻止默认返回行为
//     }
//   });
// });

// 跳转到预约挂号页面
const navigateToAppointment = () => {
  uni.navigateTo({
    url: '/pages/home/<USER>',
    animationType: 'pop-in'
  });
};

// 跳转到预约记录页面
const navigateToRecords = () => {
  uni.navigateTo({
    url: '/pages/appointment-records/index'
  });
};

// 跳转到医生评分页面
const navigateToDoctorRating = () => {
  uni.navigateTo({
    url: '/pages/doctor-rating/index'
  });
};

// 跳转到个人信息页面
const navigateToProfile = () => {
  uni.navigateTo({
    url: '/pages/patient-profile/index'
  });
};

// 跳转到健康建议页面
const navigateToProductRecommendation = () => {
  uni.navigateTo({
    url: '/pages/product-recommendation/index'
  });
};

// 退出登录方法
const logout = () => {
  uni.showModal({
    title: '确认退出',
    content: '您确定要退出登录吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({
            title: '退出中...'
          });
          
          // 调用退出登录API
          await $api.auth.logout();
          
          // 跳转到登录页面
          uni.reLaunch({
            url: '/pages/login/index'
          });
        } catch (e) {
          console.error('退出登录失败', e);
          uni.showToast({
            title: '退出失败，请重试',
            icon: 'none'
          });
        } finally {
          uni.hideLoading();
        }
      }
    }
  });
};
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #a8c0ff;
  background-image: linear-gradient(135deg, #a8c0ff 0%, #8e99f3 100%);
}

.header {
  margin-bottom: 30rpx; /* 减少底部间距 */
  padding: 30rpx 0; /* 减少内边距 */
}

.welcome-text {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 20rpx;
  text-align: center;
}

.user-info {
  display: flex;
  justify-content: center;
  align-items: center;
}

.user-name {
  font-size: 40rpx;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.options-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding-top: 20rpx; /* 减少顶部内边距，进一步往上移 */
  gap: 30rpx; /* 减少按钮间距，让布局更紧凑 */
}

.option-card {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 30rpx;
  padding: 40rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  }
}

.doctor-rating {
  position: relative;
  border: 2rpx solid #4a56e2;
  &::before {
    content: '';
    position: absolute;
    top: -2rpx;
    left: -2rpx;
    right: -2rpx;
    bottom: -2rpx;
    border-radius: 32rpx;
    border: 2rpx solid #4a56e2;
    pointer-events: none;
  }
}

.personal-info {
  position: relative;
  border: 2rpx solid #4a56e2;
  &::before {
    content: '';
    position: absolute;
    top: -2rpx;
    left: -2rpx;
    right: -2rpx;
    bottom: -2rpx;
    border-radius: 32rpx;
    border: 2rpx solid #4a56e2;
    pointer-events: none;
  }
}

.health-advice {
  position: relative;
  border: 2rpx solid #4a56e2;
  &::before {
    content: '';
    position: absolute;
    top: -2rpx;
    left: -2rpx;
    right: -2rpx;
    bottom: -2rpx;
    border-radius: 32rpx;
    border: 2rpx solid #4a56e2;
    pointer-events: none;
  }
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.option-desc {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.option-icon {
  width: 100rpx;
  height: 100rpx;
  background-color: rgba(142, 153, 243, 0.2);
  border-radius: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon {
  font-size: 50rpx;
}

/* 退出登录按钮样式 */
.logout-container {
  padding: 15rpx 0; /* 减少内边距 */
  margin-top: 20rpx; /* 减少顶部间距 */
}

.logout-button {
  background-color: rgba(255, 255, 255, 0.8);
  color: #5a67d8;
  font-size: 32rpx;
  padding: 20rpx 0;
  border-radius: 50rpx;
  border: 1rpx solid rgba(90, 103, 216, 0.3);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  }
}

.footer {
  margin-top: 10rpx; /* 减少顶部间距，让标语往上移 */
  padding: 15rpx 0; /* 减少内边距 */
  text-align: center;
}

.footer-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}
</style> 