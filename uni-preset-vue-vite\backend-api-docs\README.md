# 智慧口腔健康医疗系统 - 后端API接口文档

## 📋 文档概览

本文档集为智慧口腔健康医疗系统的完整后端API接口规范，包含了前端项目所需的所有接口定义、数据结构和实现指南。

## 🏗️ 系统架构

### 技术栈
- **后端框架**: Node.js + Express / Spring Boot / Django
- **数据库**: MySQL 8.0+ / PostgreSQL 13+
- **缓存**: Redis 6.0+
- **文件存储**: 阿里云OSS / 腾讯云COS / AWS S3
- **认证**: JWT Token
- **API风格**: RESTful

### 基础配置
- **基础URL**: `https://api.dentalcare.com/v1`
- **数据格式**: JSON
- **认证方式**: Bearer <PERSON>ken
- **字符编码**: UTF-8

## 📚 接口文档目录

### 核心业务接口

#### [01. 用户认证接口](./01-用户认证接口.md)
- 用户注册（患者/医生）
- 用户登录（统一登录）
- 密码重置
- Token验证
- 退出登录

#### [02. 患者相关接口](./02-患者相关接口.md)
- 患者信息管理
- 医生推荐算法
- 患者预约管理
- 健康数据管理
- 症状描述提交

#### [03. 医生相关接口](./03-医生相关接口.md)
- 医生信息管理
- 可预约时间管理
- 医生预约管理
- 评价管理
- 通知消息管理

#### [04. 预约相关接口](./04-预约相关接口.md)
- 预约创建与查询
- 预约状态管理
- 预约提醒设置
- 预约统计分析

#### [05. 评价相关接口](./05-评价相关接口.md)
- 评价提交与查询
- 评价互动（点赞/回复）
- 评价管理
- 评价统计

#### [06. 管理员相关接口](./06-管理员相关接口.md)
- 用户管理
- 预约管理
- 评价管理
- 系统统计
- 系统配置

#### [07. 通知相关接口](./07-通知相关接口.md)
- 通知获取与管理
- 推送通知
- 预约提醒
- 通知设置

#### [08. 文件上传接口](./08-文件上传接口.md)
- 头像上传
- 症状图片上传
- 医疗文档上传
- 文件管理
- 图片处理

#### [09. 数据库设计](./09-数据库设计.md)
- 完整数据表结构
- 索引设计
- 性能优化建议
- 数据库关系图

## 🔐 认证机制

### JWT Token 认证
```javascript
// 请求头格式
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// Token 包含信息
{
  "userId": "12345",
  "userType": "patient|doctor|admin",
  "exp": **********
}
```

### 权限控制
- **患者**: 只能访问自己的数据和公共医生信息
- **医生**: 可以访问自己的数据和相关患者预约信息
- **管理员**: 可以访问所有数据和管理功能

## 📊 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据内容
  }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "请求参数错误",
  "details": {
    "field": "phone",
    "error": "手机号格式不正确"
  }
}
```

### 分页响应
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "totalPages": 10
    }
  }
}
```

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装依赖
npm install

# 配置环境变量
cp .env.example .env

# 初始化数据库
npm run db:migrate
npm run db:seed
```

### 2. 启动服务
```bash
# 开发环境
npm run dev

# 生产环境
npm run start
```

### 3. 测试接口
```bash
# 运行测试
npm run test

# 生成API文档
npm run docs
```

## 📋 开发规范

### API设计原则
1. **RESTful风格**: 使用标准HTTP方法
2. **统一响应格式**: 所有接口返回统一的JSON格式
3. **错误处理**: 明确的错误码和错误信息
4. **版本控制**: URL中包含版本号
5. **安全性**: 所有接口都需要适当的认证和授权

### 命名规范
- **URL**: 使用小写字母和连字符
- **字段名**: 使用驼峰命名法
- **常量**: 使用大写字母和下划线

### 状态码规范
- **200**: 成功
- **201**: 创建成功
- **400**: 请求参数错误
- **401**: 未授权
- **403**: 权限不足
- **404**: 资源不存在
- **409**: 资源冲突
- **500**: 服务器内部错误

## 🔧 部署指南

### 生产环境配置
```yaml
# docker-compose.yml
version: '3.8'
services:
  api:
    image: dental-care-api:latest
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - REDIS_HOST=redis
  
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=dental_care
  
  redis:
    image: redis:6.0-alpine
```

### 监控和日志
- **API监控**: 使用 Prometheus + Grafana
- **日志收集**: 使用 ELK Stack
- **错误追踪**: 使用 Sentry
- **性能监控**: 使用 New Relic

## 📞 技术支持

### 联系方式
- **技术文档**: 本文档集
- **问题反馈**: GitHub Issues
- **技术交流**: 开发者群组

### 更新日志
- **v1.0.0**: 初始版本发布
- **v1.1.0**: 增加文件上传功能
- **v1.2.0**: 优化通知系统

---

**注意**: 本文档基于前端项目需求分析生成，涵盖了智慧口腔健康医疗系统的所有核心功能。请根据实际业务需求进行适当调整。
