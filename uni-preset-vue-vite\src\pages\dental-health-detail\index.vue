<template>
  <view class="container">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">口腔健康详情</text>
    </view>
    
    <view class="content" v-if="record">
      <!-- 检查时间 -->
      <view class="section">
        <view class="section-title">检查时间</view>
        <view class="section-content date">{{ record.created_at }}</view>
      </view>
      
      <!-- 健康评分 -->
      <view class="section">
        <view class="section-title">健康评分</view>
        <view class="score-container">
          <view class="score-circle" :class="scoreClass">
            {{ record.health_score }}
          </view>
          <view class="score-desc">
            <text>{{ scoreDescription }}</text>
          </view>
        </view>
      </view>
      
      <!-- 详细指标 -->
      <view class="section">
        <view class="section-title">详细指标</view>
        <view class="metrics-grid">
          <view class="metric-item">
            <view class="metric-label">龋齿比例</view>
            <view class="metric-value" :class="getMetricClass(record.caries_ratio)">
              {{ record.caries_ratio }}
            </view>
          </view>
          <view class="metric-item">
            <view class="metric-label">空洞比例</view>
            <view class="metric-value" :class="getMetricClass(record.cavity_ratio)">
              {{ record.cavity_ratio }}
            </view>
          </view>
          <view class="metric-item">
            <view class="metric-label">总腐蚀比例</view>
            <view class="metric-value" :class="getMetricClass(record.total_decay_ratio)">
              {{ record.total_decay_ratio }}
            </view>
          </view>
          <view class="metric-item">
            <view class="metric-label">牙菌斑比例</view>
            <view class="metric-value" :class="getMetricClass(record.plaque_ratio)">
              {{ record.plaque_ratio }}
            </view>
          </view>
        </view>
      </view>
      
      <!-- 清洁度评分 -->
      <view class="section">
        <view class="section-title">清洁度评分</view>
        <view class="cleanliness-score">
          <view class="cleanliness-bar">
            <view 
              class="cleanliness-progress" 
              :style="{ width: `${record.cleanliness_score}%` }"
              :class="getCleanlinessClass(record.cleanliness_score)"
            ></view>
          </view>
          <view class="cleanliness-value">
            {{ record.cleanliness_score }}
          </view>
        </view>
      </view>
      
      <!-- 口腔图像 -->
      <view class="section" v-if="record.img">
        <view class="section-title">口腔图像</view>
        <view class="image-container">
          <image 
            class="dental-image" 
            :src="record.img" 
            mode="aspectFit"
            @tap="previewImage"
          ></image>
        </view>
      </view>
      
      <!-- 建议 -->
      <view class="section">
        <view class="section-title">口腔健康建议</view>
        <view class="recommendation">
          <text>{{ healthRecommendation }}</text>
        </view>
      </view>
    </view>
    
    <!-- 预约复查按钮 -->
    <view class="footer-buttons">
      <button class="schedule-btn" @tap="scheduleFollowUp">预约复查</button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import dentalMock from '@/mock/dental';
import { dentalApi } from '@/api';

// 当前记录
const record = ref(null);
const recordId = ref(null);

// 获取记录详情
const fetchRecordDetails = (id) => {
  try {
    // 显示加载中
    uni.showLoading({
      title: '加载中...',
      mask: true
    });
    
    // 使用实际API接口
    dentalApi.getDentalRecords().then(res => {
      // 隐藏加载中
      uni.hideLoading();
      
      if (res.success && res.records && res.records.length > 0) {
        // 如果有ID，尝试找到匹配的记录
        if (id) {
          const foundRecord = res.records.find(r => r.id == id);
          if (foundRecord) {
            record.value = foundRecord;
            return;
          }
        }
        // 如果没有找到匹配的记录或没有ID，使用第一条记录
        record.value = res.records[0];
      } else {
        console.error('获取口腔健康记录失败', res);
        // 使用模拟数据
        useMockData(id);
      }
    }).catch(error => {
      // 隐藏加载中
      uni.hideLoading();
      console.error('获取口腔健康记录错误', error);
      
      // 使用模拟数据
      useMockData(id);
    });
  } catch (error) {
    // 隐藏加载中
    uni.hideLoading();
    console.error('获取记录详情失败', error);
    // 使用模拟数据
    useMockData(id);
  }
};

// 使用模拟数据
const useMockData = (id) => {
  console.log('使用模拟数据');
  const res = dentalMock.getDentalRecords();
  if (res.success && res.records.length > 0) {
    const foundRecord = id ? res.records.find(r => r.id == id) : null;
    record.value = foundRecord || res.records[0];
  } else {
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    });
  }
};

// 计算健康评分等级
const scoreClass = computed(() => {
  const score = record.value?.health_score || 0;
  if (score >= 85) return 'score-good';
  if (score >= 70) return 'score-medium';
  return 'score-bad';
});

// 计算健康评分描述
const scoreDescription = computed(() => {
  const score = record.value?.health_score || 0;
  if (score >= 85) return '您的口腔健康状况良好，请继续保持良好的口腔卫生习惯。';
  if (score >= 70) return '您的口腔健康状况一般，需要注意口腔卫生，定期检查。';
  return '您的口腔健康状况较差，建议尽快就医处理存在的问题。';
});

// 获取指标样式类
const getMetricClass = (value) => {
  if (!value) return '';
  
  // 将百分比字符串转为数值
  const numValue = parseFloat(value.replace('%', ''));
  
  if (numValue <= 5) return 'metric-good';
  if (numValue <= 15) return 'metric-medium';
  return 'metric-bad';
};

// 获取清洁度样式类
const getCleanlinessClass = (value) => {
  if (value >= 85) return 'cleanliness-good';
  if (value >= 70) return 'cleanliness-medium';
  return 'cleanliness-bad';
};

// 健康建议
const healthRecommendation = computed(() => {
  const score = record.value?.health_score || 0;
  const plaque = parseFloat((record.value?.plaque_ratio || '0%').replace('%', ''));
  const caries = parseFloat((record.value?.caries_ratio || '0%').replace('%', ''));
  
  let recommendation = '';
  
  if (score < 70) {
    recommendation += '您的口腔健康状况需要改善。';
  }
  
  if (plaque > 10) {
    recommendation += '牙菌斑比例较高，建议您：\n';
    recommendation += '- 每天早晚刷牙，使用含氟牙膏\n';
    recommendation += '- 使用牙线清洁牙缝\n';
    recommendation += '- 饭后漱口\n';
  }
  
  if (caries > 5) {
    recommendation += '龋齿比例较高，建议您：\n';
    recommendation += '- 减少糖分摄入\n';
    recommendation += '- 定期进行口腔检查\n';
    recommendation += '- 尽快就医处理已有龋齿\n';
  }
  
  if (recommendation === '') {
    recommendation = '您的口腔健康状况良好，请继续保持以下习惯：\n';
    recommendation += '- 每天早晚刷牙\n';
    recommendation += '- 使用牙线和漱口水\n';
    recommendation += '- 每半年进行一次口腔检查';
  }
  
  return recommendation;
});

// 预览图片
const previewImage = () => {
  if (!record.value || !record.value.img) return;
  
  uni.previewImage({
    urls: [record.value.img],
    current: record.value.img
  });
};

// 预约复查
const scheduleFollowUp = () => {
  uni.navigateTo({
    url: '/pages/doctor-selection/index'
  });
};

onMounted(() => {
  // 获取页面传参
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const id = currentPage.options?.id;
  recordId.value = id;
  
  fetchRecordDetails(id);
});
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #f8f8f8;
}

.page-header {
  margin-bottom: 30rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #4a5b8c;
  text-align: center;
  display: block;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 20rpx 0;
  border-radius: 10rpx;
  margin: 0 auto 20rpx;
  width: 80%;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(168, 192, 255, 0.3);
}

.content {
  flex: 1;
}

.section {
  background-color: #ffffff;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  border-left: 8rpx solid #4a90e2;
  padding-left: 15rpx;
}

.section-content {
  font-size: 28rpx;
  color: #666;
}

.date {
  font-size: 30rpx;
  color: #333;
}

.score-container {
  display: flex;
  align-items: center;
}

.score-circle {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40rpx;
  font-weight: 600;
  color: #ffffff;
  margin-right: 30rpx;
}

.score-good {
  background-color: #4cd964;
}

.score-medium {
  background-color: #ffcc00;
}

.score-bad {
  background-color: #ff3b30;
}

.score-desc {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.metrics-grid {
  display: flex;
  flex-wrap: wrap;
}

.metric-item {
  width: 50%;
  padding: 15rpx;
  box-sizing: border-box;
}

.metric-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.metric-value {
  font-size: 30rpx;
  font-weight: 500;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  text-align: center;
  color: #fff;
}

.metric-good {
  background-color: #4cd964;
}

.metric-medium {
  background-color: #ffcc00;
  color: #333;
}

.metric-bad {
  background-color: #ff3b30;
}

.cleanliness-score {
  display: flex;
  align-items: center;
}

.cleanliness-bar {
  flex: 1;
  height: 30rpx;
  background-color: #f0f0f0;
  border-radius: 15rpx;
  overflow: hidden;
  margin-right: 20rpx;
}

.cleanliness-progress {
  height: 100%;
  border-radius: 15rpx;
}

.cleanliness-good {
  background-color: #4cd964;
}

.cleanliness-medium {
  background-color: #ffcc00;
}

.cleanliness-bad {
  background-color: #ff3b30;
}

.cleanliness-value {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  width: 60rpx;
  text-align: center;
}

.image-container {
  width: 100%;
  display: flex;
  justify-content: center;
}

.dental-image {
  width: 600rpx;
  height: 400rpx;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.recommendation {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  white-space: pre-line;
}

.footer-buttons {
  margin-top: 30rpx;
  padding: 20rpx 0;
}

.schedule-btn {
  width: 100%;
  height: 90rpx;
  background-color: #4a90e2;
  color: #ffffff;
  font-size: 32rpx;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
}

.schedule-btn:active {
  transform: scale(0.98);
  background-color: #3a80d2;
}
</style> 