<template>
  <view class="container">
    <view class="header">
      <view class="back-button" @tap="goBack">
        <text class="back-icon">←</text>
      </view>
      <text class="title">{{ getPageTitle }}</text>
    </view>
    
    <view class="content">
      <view class="form-card">
        <!-- 基本信息 -->
        <view class="form-section">
          <view class="section-title">基本信息</view>
          
          <view class="form-item">
            <text class="form-label">姓名</text>
            <input class="form-input" v-model="doctorInfo.name" placeholder="请输入医生姓名" :disabled="isViewMode" />
          </view>

          <view class="form-item">
            <text class="form-label">性别</text>
            <picker v-if="!isViewMode" class="form-picker" :range="genderOptions" @change="onGenderChange">
              <view class="picker-value">{{ doctorInfo.gender || '请选择' }}</view>
            </picker>
            <view v-else class="form-input disabled">{{ doctorInfo.gender || '未设置' }}</view>
          </view>

          <view class="form-item">
            <text class="form-label">电话号码</text>
            <input class="form-input" v-model="doctorInfo.phone" placeholder="请输入电话号码" type="number" maxlength="11" :disabled="isViewMode" />
          </view>

          <view class="form-item" v-if="!isViewMode">
            <text class="form-label">密码</text>
            <view class="password-input-container">
              <input
                class="form-input"
                v-model="doctorInfo.password"
                placeholder="请输入密码"
                :type="showPassword ? 'text' : 'password'"
              />
              <text class="password-toggle" @tap="togglePasswordVisibility">
                {{ showPassword ? '隐藏' : '显示' }}
              </text>
            </view>
          </view>

          <view class="form-item">
            <text class="form-label">邮箱</text>
            <input class="form-input" v-model="doctorInfo.email" placeholder="请输入邮箱" :disabled="isViewMode" />
          </view>
        </view>

        <!-- 专业信息 - 仅在非查看模式下显示 -->
        <view class="form-section" v-if="!isViewMode">
          <view class="section-title">专业信息</view>

          <view class="form-item">
            <text class="form-label">诊所位置</text>
            <input class="form-input" v-model="doctorInfo.clinicLocation" placeholder="请输入诊所位置" />
          </view>

          <view class="form-item">
            <text class="form-label">擅长专科</text>
            <input class="form-input" v-model="doctorInfo.specialization" placeholder="请输入擅长专科" />
          </view>

          <view class="form-item full">
            <text class="form-label">个人简介</text>
            <textarea class="form-textarea" v-model="doctorInfo.introduction" placeholder="请输入个人简介" />
          </view>
        </view>

        <!-- 操作按钮 - 仅在非查看模式下显示 -->
        <view class="form-actions" v-if="!isViewMode">
          <button class="action-button cancel" @tap="goBack">取消</button>
          <button class="action-button save" @tap="saveDoctor">保存</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';

// 页面参数
const query = ref({
  mode: 'add',
  id: ''
});

// 是否为编辑模式
const isEdit = computed(() => query.value.mode === 'edit');

// 是否为查看模式
const isViewMode = computed(() => query.value.mode === 'view');

// 页面标题
const getPageTitle = computed(() => {
  if (isViewMode.value) return '医生信息';
  if (isEdit.value) return '编辑医生信息';
  return '添加医生';
});

// 是否显示密码
const showPassword = ref(false);

// 医生信息
const doctorInfo = ref({
  id: '',
  name: '',
  gender: '',
  phone: '',
  password: '',
  email: '',
  clinicLocation: '',
  specialization: '',
  introduction: ''
});

// 选项数据
const genderOptions = ['男', '女'];

// 页面加载时
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  if (currentPage && currentPage.$page && currentPage.$page.fullPath) {
    const path = currentPage.$page.fullPath;
    const queryParams = path.split('?')[1];
    if (queryParams) {
      queryParams.split('&').forEach(param => {
        const [key, value] = param.split('=');
        if (key && value) {
          query.value[key] = decodeURIComponent(value);
        }
      });
    }
  }
  
  // 如果是编辑或查看模式，获取医生信息
  if ((isEdit.value || isViewMode.value) && query.value.id) {
    try {
      const storageKey = isViewMode.value ? 'currentViewDoctor' : 'currentEditDoctor';
      const doctor = uni.getStorageSync(storageKey);
      if (doctor) {
        doctorInfo.value = { ...doctorInfo.value, ...doctor };
      }
    } catch (e) {
      console.error('获取医生信息失败', e);
    }
  } else {
    // 如果是添加模式，生成一个新的医生ID
    doctorInfo.value.id = generateDoctorId();
  }
});

// 性别选择变化
const onGenderChange = (e) => {
  const index = e.detail.value;
  doctorInfo.value.gender = genderOptions[index];
};

// 切换密码可见性
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
};

// 生成医生ID
const generateDoctorId = () => {
  const timestamp = new Date().getTime();
  return 'D' + timestamp.toString().substring(6);
};

// 保存医生信息
const saveDoctor = () => {
  // 表单验证
  if (!doctorInfo.value.name) {
    uni.showToast({
      title: '请输入医生姓名',
      icon: 'none'
    });
    return;
  }
  
  if (!doctorInfo.value.phone) {
    uni.showToast({
      title: '请输入电话号码',
      icon: 'none'
    });
    return;
  }
  
  if (!doctorInfo.value.password) {
    uni.showToast({
      title: '请输入密码',
      icon: 'none'
    });
    return;
  }
  
  if (!doctorInfo.value.specialization) {
    uni.showToast({
      title: '请输入擅长专科',
      icon: 'none'
    });
    return;
  }
  
  // 保存医生信息
  try {
    // 实际应用中应该调用API保存到服务器
    // 这里仅做模拟
    setTimeout(() => {
      uni.showToast({
        title: isEdit.value ? '更新成功' : '添加成功',
        icon: 'success'
      });
      
      // 返回上一页
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }, 500);
  } catch (e) {
    console.error('保存医生信息失败', e);
    uni.showToast({
      title: '保存失败，请重试',
      icon: 'none'
    });
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
}

.header {
  background-color: #a8c0ff;
  background-image: linear-gradient(135deg, #a8c0ff 0%, #8e99f3 100%);
  padding: 40rpx 30rpx;
  color: #fff;
  display: flex;
  align-items: center;
  position: relative;
}

.back-button {
  position: absolute;
  left: 30rpx;
  top: 50%;
  transform: translateY(-50%);
}

.back-icon {
  font-size: 40rpx;
  color: #fff;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  flex: 1;
  text-align: center;
}

.content {
  flex: 1;
  padding: 30rpx;
}

.form-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.form-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.form-item {
  margin-bottom: 25rpx;
}

.form-item.full {
  width: 100%;
}

.form-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.form-input {
  background-color: #f5f7fa;
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  width: 100%;
  box-sizing: border-box;
  border: 1rpx solid #eee;
}

.form-input:disabled,
.form-input.disabled {
  background-color: #f0f0f0;
  color: #999;
  cursor: not-allowed;
}

.password-input-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 26rpx;
  color: #8e99f3;
}

.form-picker {
  background-color: #f5f7fa;
  padding: 20rpx;
  border-radius: 10rpx;
  width: 100%;
  box-sizing: border-box;
  border: 1rpx solid #eee;
}

.picker-value {
  font-size: 28rpx;
  color: #333;
}

.form-textarea {
  background-color: #f5f7fa;
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  width: 100%;
  height: 200rpx;
  box-sizing: border-box;
  border: 1rpx solid #eee;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  gap: 30rpx;
  margin-top: 40rpx;
}

.action-button {
  flex: 1;
  font-size: 32rpx;
  padding: 20rpx 0;
  border-radius: 10rpx;
  border: none;
}

.action-button.cancel {
  background-color: #f5f5f5;
  color: #666;
}

.action-button.save {
  background-color: #8e99f3;
  color: #fff;
}
</style> 