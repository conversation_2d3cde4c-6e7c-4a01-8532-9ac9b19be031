import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    uni(),
  ],
  server: {
    port: 5173,
    proxy: {
      // 配置代理
      '/api': {
        target: 'https://locokdvuripk.sealosbja.site',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '/api'),
        // 解决HTTPS证书问题
        secure: false
      }
    }
  }
})
