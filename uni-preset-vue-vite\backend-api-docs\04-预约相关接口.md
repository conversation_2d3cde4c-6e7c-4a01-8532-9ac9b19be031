# 预约相关接口

## 基础信息
- **基础URL**: `https://api.dentalcare.com/v1`
- **认证方式**: JWT <PERSON> (Bearer Authentication)
- **请求头**: `Authorization: Bearer {token}`

## 1. 预约创建

### 1.1 创建预约
- **URL**: `/appointments`
- **方法**: `POST`
- **描述**: 患者创建新预约
- **请求参数**:
```json
{
  "doctorId": "doc001",
  "timeSlotId": "ts001",
  "appointmentTime": "2023-11-20T14:00:00Z",
  "appointmentType": "consultation",
  "symptoms": "牙齿不齐，想要矫正",
  "notes": "初诊咨询，希望了解矫正方案",
  "contactPhone": "13800138000",
  "emergencyContact": {
    "name": "李四",
    "phone": "13900139000",
    "relationship": "配偶"
  }
}
```

- **成功响应**:
```json
{
  "code": 200,
  "message": "预约创建成功",
  "data": {
    "appointmentId": "apt001",
    "appointmentNumber": "AP20231120001",
    "status": "pending",
    "doctorName": "李医生",
    "hospital": "北京口腔医院",
    "appointmentTime": "2023-11-20T14:00:00Z",
    "estimatedDuration": 30,
    "createdAt": "2023-11-15T10:00:00Z"
  }
}
```

### 1.2 检查时间段可用性
- **URL**: `/appointments/check-availability`
- **方法**: `POST`
- **描述**: 检查指定时间段是否可预约
- **请求参数**:
```json
{
  "doctorId": "doc001",
  "timeSlotId": "ts001",
  "appointmentTime": "2023-11-20T14:00:00Z"
}
```

- **成功响应**:
```json
{
  "code": 200,
  "message": "时间段可用",
  "data": {
    "available": true,
    "timeSlotInfo": {
      "timeSlotId": "ts001",
      "startTime": "14:00",
      "endTime": "14:30",
      "maxPatients": 1,
      "bookedPatients": 0
    }
  }
}
```

## 2. 预约查询

### 2.1 获取预约详情
- **URL**: `/appointments/{appointmentId}`
- **方法**: `GET`
- **描述**: 获取指定预约的详细信息
- **路径参数**:
  - `appointmentId`: 预约ID

- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "appointmentId": "apt001",
    "appointmentNumber": "AP20231120001",
    "patientId": "pat001",
    "patientName": "张三",
    "patientPhone": "13800138000",
    "doctorId": "doc001",
    "doctorName": "李医生",
    "specialty": "口腔正畸",
    "hospital": "北京口腔医院",
    "department": "正畸科",
    "appointmentTime": "2023-11-20T14:00:00Z",
    "appointmentType": "consultation",
    "status": "confirmed",
    "symptoms": "牙齿不齐，想要矫正",
    "notes": "初诊咨询，希望了解矫正方案",
    "doctorNotes": "已确认预约，请按时到诊",
    "estimatedDuration": 30,
    "actualDuration": null,
    "createdAt": "2023-11-15T10:00:00Z",
    "updatedAt": "2023-11-15T11:00:00Z",
    "emergencyContact": {
      "name": "李四",
      "phone": "13900139000",
      "relationship": "配偶"
    }
  }
}
```

### 2.2 获取预约列表
- **URL**: `/appointments`
- **方法**: `GET`
- **描述**: 获取预约列表（支持多种筛选条件）
- **查询参数**:
  - `patientId`: 患者ID（可选）
  - `doctorId`: 医生ID（可选）
  - `status`: 预约状态（pending/confirmed/completed/cancelled）
  - `startDate`: 开始日期
  - `endDate`: 结束日期
  - `appointmentType`: 预约类型
  - `page`: 页码
  - `limit`: 每页数量

- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "appointments": [
      {
        "appointmentId": "apt001",
        "appointmentNumber": "AP20231120001",
        "patientName": "张三",
        "doctorName": "李医生",
        "hospital": "北京口腔医院",
        "appointmentTime": "2023-11-20T14:00:00Z",
        "status": "confirmed",
        "appointmentType": "consultation"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    }
  }
}
```

## 3. 预约状态管理

### 3.1 取消预约
- **URL**: `/appointments/{appointmentId}/cancel`
- **方法**: `PUT`
- **描述**: 取消预约
- **请求参数**:
```json
{
  "reason": "临时有事无法前往",
  "notes": "希望能重新预约"
}
```

- **成功响应**:
```json
{
  "code": 200,
  "message": "预约已取消",
  "data": {
    "appointmentId": "apt001",
    "status": "cancelled",
    "cancelledAt": "2023-11-16T09:00:00Z",
    "refundInfo": {
      "refundable": true,
      "refundAmount": 0,
      "refundMethod": "original_payment"
    }
  }
}
```

### 3.2 重新预约
- **URL**: `/appointments/{appointmentId}/reschedule`
- **方法**: `PUT`
- **描述**: 重新安排预约时间
- **请求参数**:
```json
{
  "newTimeSlotId": "ts005",
  "newAppointmentTime": "2023-11-22T14:00:00Z",
  "reason": "原时间有冲突"
}
```

### 3.3 完成预约
- **URL**: `/appointments/{appointmentId}/complete`
- **方法**: `PUT`
- **描述**: 标记预约为已完成
- **请求参数**:
```json
{
  "actualDuration": 45,
  "treatmentSummary": "完成初诊检查，制定矫正方案",
  "nextAppointmentSuggestion": "2023-12-01T14:00:00Z",
  "prescriptions": [
    {
      "medication": "漱口水",
      "dosage": "每日2次",
      "duration": "1周"
    }
  ]
}
```

## 4. 预约提醒

### 4.1 设置预约提醒
- **URL**: `/appointments/{appointmentId}/reminders`
- **方法**: `POST`
- **描述**: 设置预约提醒
- **请求参数**:
```json
{
  "reminders": [
    {
      "type": "sms",
      "timing": "1_day_before",
      "enabled": true
    },
    {
      "type": "push",
      "timing": "1_hour_before",
      "enabled": true
    }
  ]
}
```

### 4.2 获取预约提醒设置
- **URL**: `/appointments/{appointmentId}/reminders`
- **方法**: `GET`
- **描述**: 获取预约提醒设置

## 5. 预约统计

### 5.1 获取预约统计数据
- **URL**: `/appointments/statistics`
- **方法**: `GET`
- **描述**: 获取预约相关统计数据
- **查询参数**:
  - `startDate`: 开始日期
  - `endDate`: 结束日期
  - `doctorId`: 医生ID（可选）
  - `patientId`: 患者ID（可选）

- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "totalAppointments": 150,
    "completedAppointments": 120,
    "cancelledAppointments": 20,
    "pendingAppointments": 10,
    "completionRate": 80.0,
    "cancellationRate": 13.3,
    "averageDuration": 35,
    "busyHours": [
      {"hour": "09:00", "count": 25},
      {"hour": "14:00", "count": 30}
    ],
    "popularSpecialties": [
      {"specialty": "口腔正畸", "count": 45},
      {"specialty": "口腔修复", "count": 35}
    ]
  }
}
```
