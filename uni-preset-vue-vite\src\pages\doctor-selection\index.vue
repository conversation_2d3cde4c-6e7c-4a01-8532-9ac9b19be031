<template>
  <view class="container">
    <!-- 页面标题 -->
    <view class="page-title">为您推荐更多医生</view>

    <!-- 区域信息 -->
    <view class="area-info">
      <text class="area-text">{{ selectedArea }}</text>
    </view>

    <!-- 专科分类 -->
    <view class="specialty-section">
      <view class="specialty-title">推荐专科</view>
      <view class="specialty-tags">
        <view
          class="specialty-tag"
          v-for="specialty in prioritySpecialties"
          :key="specialty"
          :class="{ 'priority': true }"
        >
          <text class="specialty-name">{{ specialty }}</text>
        </view>
      </view>
    </view>

    <!-- 医生列表 -->
    <scroll-view class="doctor-list" scroll-y>
      <view
        class="doctor-item"
        v-for="(doctor, index) in sortedDoctors"
        :key="doctor.id"
        @tap="selectDoctor(doctor)"
      >
        <view class="doctor-header">
          <view class="doctor-basic">
            <view class="doctor-name">{{ doctor.name }}</view>
            <view class="doctor-title">{{ doctor.title }}</view>
          </view>
          <view class="doctor-badges">
            <view class="specialty-badge" :class="{ 'priority': isPrioritySpecialty(doctor.specialty) }">
              {{ doctor.specialty }}
            </view>
          </view>
        </view>

        <view class="doctor-details">
          <view class="hospital-info">
            <text class="hospital-name">{{ doctor.hospital }}</text>
            <text class="area-badge">{{ doctor.area }}</text>
          </view>

          <view class="doctor-stats">
            <view class="rating-section">
              <view class="stars">
                <text class="star" v-for="i in 5" :key="i" :class="{ 'filled': i <= doctor.rating }">★</text>
              </view>
              <text class="rating-text">{{ doctor.rating }}.0</text>
            </view>
            <text class="experience-text">{{ doctor.experience }}年经验</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 响应式数据
const selectedArea = ref('');
const prioritySpecialties = ref([]);
const sourceType = ref(''); // 'home' 或 'situation'

// 所有医生数据
const allDoctors = ref([
  // 漳州市芗城区医生
  {
    id: 1,
    name: '张志强',
    title: '主任医师',
    specialty: '口腔内科',
    hospital: '漳州市医院',
    area: '漳州市芗城区',
    rating: 5,
    experience: 18
  },
  {
    id: 2,
    name: '李美华',
    title: '副主任医师',
    specialty: '牙周病科',
    hospital: '漳州市第一医院',
    area: '漳州市芗城区',
    rating: 5,
    experience: 15
  },
  {
    id: 3,
    name: '王建国',
    title: '主治医师',
    specialty: '口腔正畸科',
    hospital: '漳州市中医院',
    area: '漳州市芗城区',
    rating: 4,
    experience: 12
  },
  {
    id: 4,
    name: '陈丽萍',
    title: '主任医师',
    specialty: '口腔外科',
    hospital: '漳州市人民医院',
    area: '漳州市芗城区',
    rating: 5,
    experience: 20
  },
  {
    id: 5,
    name: '林晓东',
    title: '副主任医师',
    specialty: '口腔种植科',
    hospital: '漳州口腔专科医院',
    area: '漳州市芗城区',
    rating: 4,
    experience: 14
  },
  {
    id: 6,
    name: '黄雅琴',
    title: '主治医师',
    specialty: '口腔美容科',
    hospital: '漳州美齿口腔诊所',
    area: '漳州市芗城区',
    rating: 4,
    experience: 10
  },
  {
    id: 7,
    name: '吴明辉',
    title: '主治医师',
    specialty: '儿童口腔科',
    hospital: '漳州市妇幼保健院',
    area: '漳州市芗城区',
    rating: 4,
    experience: 8
  },
  {
    id: 8,
    name: '郑小燕',
    title: '副主任医师',
    specialty: '口腔修复科',
    hospital: '漳州市口腔医院',
    area: '漳州市芗城区',
    rating: 5,
    experience: 16
  },

  // 福州市鼓楼区医生
  {
    id: 9,
    name: '郑文斌',
    title: '主任医师',
    specialty: '口腔内科',
    hospital: '福建省立医院',
    area: '福州市鼓楼区',
    rating: 5,
    experience: 22
  },
  {
    id: 10,
    name: '吴秀兰',
    title: '副主任医师',
    specialty: '牙周病科',
    hospital: '福州市第一医院',
    area: '福州市鼓楼区',
    rating: 5,
    experience: 16
  },
  {
    id: 11,
    name: '刘志华',
    title: '主治医师',
    specialty: '口腔正畸科',
    hospital: '福建医科大学附属口腔医院',
    area: '福州市鼓楼区',
    rating: 4,
    experience: 11
  },
  {
    id: 12,
    name: '许明辉',
    title: '主任医师',
    specialty: '口腔外科',
    hospital: '福州市中医院',
    area: '福州市鼓楼区',
    rating: 5,
    experience: 19
  }
]);

// 判断是否为优先专科
const isPrioritySpecialty = (specialty) => {
  return prioritySpecialties.value.includes(specialty);
};

// 智能排序医生列表
const sortedDoctors = computed(() => {
  console.log('=== 医生选择页面排序 ===');
  console.log('选择区域:', selectedArea.value);
  console.log('优先专科:', prioritySpecialties.value);
  console.log('来源类型:', sourceType.value);

  // 筛选当前区域的医生
  const areaFilteredDoctors = allDoctors.value.filter(doctor =>
    doctor.area === selectedArea.value
  );

  console.log('当前区域医生数量:', areaFilteredDoctors.length);

  // 按专科优先级和评分排序
  const sorted = areaFilteredDoctors.sort((a, b) => {
    // 优先级1: 优先专科
    const aIsPriority = isPrioritySpecialty(a.specialty);
    const bIsPriority = isPrioritySpecialty(b.specialty);

    if (aIsPriority && !bIsPriority) return -1;
    if (!aIsPriority && bIsPriority) return 1;

    // 优先级2: 评分
    if (a.rating !== b.rating) return b.rating - a.rating;

    // 优先级3: 经验
    return b.experience - a.experience;
  });

  console.log('排序后医生列表:');
  sorted.forEach((doctor, index) => {
    const priority = isPrioritySpecialty(doctor.specialty) ? '优先专科' : '其他专科';
    console.log(`${index + 1}. ${doctor.name} - ${doctor.specialty} - ${priority}`);
  });

  return sorted;
});

// 选择医生
const selectDoctor = (doctor) => {
  uni.showToast({
    title: `已选择${doctor.name}`,
    icon: 'success'
  });

  // 保存选择的医生信息
  uni.setStorageSync('selectedDoctor', doctor);

  // 跳转到预约详情页面
  setTimeout(() => {
    uni.navigateTo({
      url: '/pages/appointment-detail/index'
    });
  }, 1500);
};

// 页面加载时获取参数和数据
onMounted(() => {
  console.log('=== 医生选择页面加载 ===');

  // 获取用户选择的区域
  const userAddress = uni.getStorageSync('userAddress');
  if (userAddress) {
    selectedArea.value = userAddress;
  } else {
    selectedArea.value = '漳州市芗城区'; // 默认区域
  }

  // 获取页面参数（从URL参数或本地存储）
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};

  console.log('页面参数:', options);

  // 判断来源页面和获取推荐专科
  if (options.specialties) {
    // 从情况描述页面跳转过来
    sourceType.value = 'situation';
    prioritySpecialties.value = options.specialties.split(',');
    console.log('来源: 情况描述页面');
  } else {
    // 从预约就医页面跳转过来，获取推荐的专科
    sourceType.value = 'home';
    // 这里可以根据预约就医页面的分析结果获取推荐专科
    // 暂时使用默认专科
    prioritySpecialties.value = ['口腔内科', '牙周病科'];
    console.log('来源: 预约就医页面');
  }

  if (options.area) {
    selectedArea.value = options.area;
  }

  console.log('最终设置 - 区域:', selectedArea.value);
  console.log('最终设置 - 优先专科:', prioritySpecialties.value);
  console.log('最终设置 - 来源类型:', sourceType.value);
});
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #a8c0ff;
  background-image: linear-gradient(135deg, #a8c0ff 0%, #8e99f3 100%);
}

.page-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #4a5b8c;
  text-align: center;
  margin-bottom: 20rpx;
  padding: 20rpx 0;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(168, 192, 255, 0.3);
}

.area-info {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 15rpx 30rpx;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 25rpx;
  border: 2rpx solid rgba(168, 192, 255, 0.4);
}

.area-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #4a90e2;
}

.specialty-section {
  margin-bottom: 30rpx;
}

.specialty-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #4a5b8c;
  margin-bottom: 15rpx;
  text-align: center;
}

.specialty-tags {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 15rpx;
}

.specialty-tag {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border: 2rpx solid #4a90e2;

  &.priority {
    background-color: #4a90e2;
    color: white;
  }
}

.specialty-name {
  font-size: 24rpx;
  font-weight: 500;
  color: inherit;
}

.doctor-list {
  flex: 1;
  width: 100%;
  height: calc(100vh - 300rpx);
}

.doctor-item {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 25rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    background-color: rgba(255, 255, 255, 1);
    border-color: #4a90e2;
    box-shadow: 0 8rpx 25rpx rgba(74, 144, 226, 0.2);
  }
}

.doctor-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.doctor-basic {
  flex: 1;
}

.doctor-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.doctor-title {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.doctor-badges {
  display: flex;
  align-items: center;
}

.specialty-badge {
  padding: 8rpx 16rpx;
  border-radius: 15rpx;
  font-size: 22rpx;
  font-weight: 500;
  background-color: #f0f8ff;
  color: #4a90e2;
  border: 1rpx solid #e6f3ff;

  &.priority {
    background-color: #4a90e2;
    color: white;
    border-color: #4a90e2;
  }
}

.doctor-details {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.hospital-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10rpx;
}

.hospital-name {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

.area-badge {
  font-size: 22rpx;
  color: #4a90e2;
  background-color: #f0f8ff;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid #e6f3ff;
}

.doctor-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.rating-section {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stars {
  display: flex;
  gap: 2rpx;
}

.star {
  font-size: 24rpx;
  color: #ddd;

  &.filled {
    color: #ffd700;
  }
}

.rating-text {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  margin-left: 5rpx;
}

.experience-text {
  font-size: 24rpx;
  color: #666;
}
</style>