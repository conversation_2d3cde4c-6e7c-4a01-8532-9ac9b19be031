/**
 * 请求工具类
 */

// API基础URL - 开发环境使用代理，生产环境使用实际URL
const BASE_URL = process.env.NODE_ENV === 'development' 
  ? '/api' 
  : 'https://locokdvuripk.sealosbja.site/api';

// 存储Token的键名
const TOKEN_KEY = 'DENTAL_HEALTH_TOKEN';

/**
 * 获取存储的Token
 */
const getToken = () => {
  return uni.getStorageSync(TOKEN_KEY) || '';
};

/**
 * 设置Token到本地存储
 * @param {string} token - JWT令牌
 * @param {number} expireTime - 过期时间戳
 */
const setToken = (token, expireTime) => {
  uni.setStorageSync(TOKEN_KEY, token);
  // 存储过期时间
  if (expireTime) {
    uni.setStorageSync('TOKEN_EXPIRE_TIME', expireTime);
  }
};

/**
 * 清除存储的Token
 */
const clearToken = () => {
  uni.removeStorageSync(TOKEN_KEY);
  uni.removeStorageSync('TOKEN_EXPIRE_TIME');
};

/**
 * 通用请求方法
 * @param {Object} options - 请求配置项
 * @returns {Promise} - 返回Promise对象
 */
const request = (options) => {
  // 添加重试逻辑
  const retry = options.retry || 0;
  const retryDelay = options.retryDelay || 1000;
  let retryCount = 0;
  
  // 创建请求函数
  const createRequest = () => {
  return new Promise((resolve, reject) => {
      // 构建请求URL - 如果URL以http开头，则使用完整URL，否则添加BASE_URL
      const url = options.url.startsWith('http') ? options.url : BASE_URL + options.url;
    
    // 构建请求头
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers
    };
    
    // 如果需要添加认证Token（除非特别指定不需要）
    if (options.auth !== false) {
      const token = getToken();
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }
    }
    
    console.log(`发送${options.method || 'GET'}请求到: ${url}`, options.data || '无数据');
    
    // 发起请求
    uni.request({
      url,
      data: options.data,
      method: options.method || 'GET',
      header: headers,
      // 允许跨域携带Cookie
      withCredentials: true,
        timeout: options.timeout || 15000, // 使用配置的超时时间或默认15秒
        // 如果有自定义响应转换函数，则使用它
        dataType: options.transformResponse ? 'text' : 'json',
      success: (res) => {
        console.log(`请求响应: ${url}`, res);
          
          // 如果有自定义转换函数，先处理响应数据
          if (options.transformResponse && options.transformResponse.length > 0) {
            let transformedData = res.data;
            for (const transformFn of options.transformResponse) {
              transformedData = transformFn(transformedData);
            }
            res.data = transformedData;
          }
        
        // 处理HTTP状态码
        if (res.statusCode >= 200 && res.statusCode < 300) {
            // 处理业务状态码，如果是JSON对象并且有code字段
            if (res.data && typeof res.data === 'object' && res.data.code !== undefined) {
              if (res.data.code === 200) {
            resolve(res.data);
          } else {
            // 处理业务错误
            const errorMsg = (res.data && res.data.message) || '请求失败';
            
            // 特殊处理401未授权的情况（Token过期）
            if (res.data && res.data.code === 401) {
              clearToken();
              // 重定向到登录页
              uni.showToast({
                title: '登录已过期，请重新登录',
                icon: 'none',
                duration: 1500,
                complete: () => {
                  setTimeout(() => {
                    uni.reLaunch({
                      url: '/pages/login/index'
                    });
                  }, 1500);
                }
              });
            } else {
              // 显示错误提示
              uni.showToast({
                title: errorMsg,
                icon: 'none'
              });
            }
            
            reject(res.data);
              }
            } else {
              // 如果没有code字段，则直接返回数据
              resolve(res.data);
          }
        } else {
          // 处理HTTP错误
          let errorMsg = '网络请求失败';
          if (res.statusCode === 404) {
            errorMsg = '请求的资源不存在';
          } else if (res.statusCode === 500) {
            errorMsg = '服务器内部错误';
          } else if (res.statusCode === 503) {
            errorMsg = '服务暂时不可用，请稍后再试';
          } else if (res.statusCode === 403) {
            errorMsg = '没有权限访问该资源';
          }
          
          console.error(`请求失败: ${url}`, res);
          
          uni.showToast({
            title: errorMsg,
            icon: 'none'
          });
          
          reject({
            code: res.statusCode,
            message: errorMsg
          });
        }
      },
      fail: (err) => {
        console.error('请求失败:', url, err);
        
        // 处理不同类型的错误
        let errorMsg = '网络连接失败，请检查网络';
        
        if (err.errMsg.includes('timeout')) {
          errorMsg = '请求超时，请稍后重试';
        } else if (err.errMsg.includes('ssl')) {
          errorMsg = 'SSL证书验证失败';
        }
          
          // 如果设置了重试且未达到最大重试次数
          if (retry > 0 && retryCount < retry) {
            retryCount++;
            console.log(`第${retryCount}次重试请求: ${url}`);
            
            // 延迟后重试
            setTimeout(() => {
              resolve(createRequest());
            }, retryDelay);
            return;
          }
        
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
        
        reject(err);
      }
    });
  });
  };
  
  // 开始请求
  return createRequest();
};

/**
 * GET请求
 * @param {string} url - 请求路径
 * @param {Object} params - URL参数
 * @param {Object} options - 其他选项
 */
const get = (url, params = {}, options = {}) => {
  return request({
    url,
    method: 'GET',
    data: params,
    ...options
  });
};

/**
 * POST请求
 * @param {string} url - 请求路径
 * @param {Object} data - 请求体数据
 * @param {Object} options - 其他选项
 */
const post = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  });
};

/**
 * PUT请求
 * @param {string} url - 请求路径
 * @param {Object} data - 请求体数据
 * @param {Object} options - 其他选项
 */
const put = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  });
};

/**
 * DELETE请求
 * @param {string} url - 请求路径
 * @param {Object} params - URL参数
 * @param {Object} options - 其他选项
 */
const del = (url, params = {}, options = {}) => {
  return request({
    url,
    method: 'DELETE',
    data: params,
    ...options
  });
};

export {
  request,
  get,
  post,
  put,
  del,
  getToken,
  setToken,
  clearToken,
  BASE_URL
}; 

// 添加默认导出
export default request; 