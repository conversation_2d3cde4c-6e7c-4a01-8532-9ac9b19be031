<template>
  <view class="container">
    <!-- 用户信息 -->
    <view class="user-info">
      <view class="phone-number">{{ userInfo.phone || '暂无电话号码' }}</view>
    </view>
    
    <!-- 预约提醒 -->
    <view class="reminder-section">
      <view class="reminder-title">预约提醒</view>
      <view class="reminder-content">
        每人每次只能拥有一次预约，如需修改预约请先取消预约并重新预约
      </view>
    </view>
    
    <!-- 预约详情 -->
    <view class="detail-card">
      <view class="detail-item">
        <view class="detail-label">预约时间</view>
        <view class="detail-value time-value">{{ appointmentDetail.appointmentTime }}</view>
      </view>
      <view class="detail-item">
        <view class="detail-label">专科</view>
        <view class="detail-value">{{ appointmentDetail.specialty }}</view>
      </view>
      <view class="detail-item">
        <view class="detail-label">就诊医生</view>
        <view class="detail-value">{{ appointmentDetail.doctorName }}</view>
      </view>
      <view class="detail-item">
        <view class="detail-label">诊所地址</view>
        <view class="detail-value">{{ appointmentDetail.clinicAddress }}</view>
      </view>
      <view class="appointment-status" :class="getStatusClass(appointmentDetail.status)">
        {{ getStatusText(appointmentDetail.status) }}
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="btn-section">
      <button 
        v-if="appointmentDetail.status === 'upcoming'" 
        class="cancel-btn" 
        @tap="cancelAppointment"
      >
        取消预约
      </button>
      <button 
        v-else-if="appointmentDetail.status === 'cancelled'" 
        class="reappoint-btn" 
        @tap="reappointment"
      >
        重新预约
      </button>
      <button 
        v-else
        class="return-btn" 
        @tap="returnToRecords"
      >
        返回记录
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 用户信息
const userInfo = ref({
  phone: '',
  name: ''
});

// 预约详情
const appointmentDetail = ref({
  id: '',
  appointmentTime: '',
  specialty: '',
  doctorName: '',
  clinicAddress: '',
  status: 'upcoming' // upcoming, completed, cancelled
});

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 'upcoming':
      return 'status-upcoming';
    case 'completed':
      return 'status-completed';
    case 'cancelled':
      return 'status-cancelled';
    default:
      return '';
  }
};

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'upcoming':
      return '即将就诊';
    case 'completed':
      return '已完成';
    case 'cancelled':
      return '已取消';
    default:
      return '未知状态';
  }
};

// 取消预约
const cancelAppointment = () => {
  uni.showModal({
    title: '确认取消',
    content: '您确定要取消此次预约吗？',
    success: (res) => {
      if (res.confirm) {
        // 显示加载中
        uni.showLoading({
          title: '取消中...'
        });
        
        // 模拟取消请求
        setTimeout(() => {
          uni.hideLoading();
          
          // 更新预约状态
          appointmentDetail.value.status = 'cancelled';
          
          // 更新存储的预约信息
          try {
            // 在实际项目中，这里应该是API调用更新预约状态
            const appointmentInfo = uni.getStorageSync('appointmentInfo');
            if (appointmentInfo) {
              appointmentInfo.status = 'cancelled';
              uni.setStorageSync('appointmentInfo', appointmentInfo);
            }
          } catch (e) {
            console.error('更新预约信息失败', e);
          }
          
          uni.showToast({
            title: '预约已取消',
            icon: 'success'
          });
          
          // 延迟跳转回预约记录页面
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        }, 2000);
      }
    }
  });
};

// 重新预约
const reappointment = () => {
  uni.showModal({
    title: '重新预约',
    content: '您将开始新的预约流程，是否继续？',
    success: (res) => {
      if (res.confirm) {
        // 跳转到预约首页
        uni.navigateTo({
          url: '/pages/home/<USER>'
        });
      }
    }
  });
};

// 返回预约记录
const returnToRecords = () => {
  uni.navigateBack();
};

// 页面加载时
onMounted(() => {
  // 获取用户信息
  try {
    const user = uni.getStorageSync('userInfo');
    if (user) {
      userInfo.value = user;
    }
  } catch (e) {
    console.error('获取用户信息失败', e);
  }
  
  // 获取预约详情
  const appointmentId = uni.getStorageSync('currentAppointmentId');
  if (appointmentId) {
    // 在实际项目中，这里应该是API调用获取预约详情
    // 这里使用模拟数据
    const mockAppointments = {
      '1001': {
        id: '1001',
        appointmentTime: '2025-07-10 09:00-10:00',
        specialty: '正畸专科',
        doctorName: '张医生',
        clinicAddress: '北京市海淀区中关村大街1号医疗中心',
        status: 'upcoming'
      },
      '1002': {
        id: '1002',
        appointmentTime: '2025-07-05 14:30-15:30',
        specialty: '牙周专科',
        doctorName: '李医生',
        clinicAddress: '北京市西城区西单北大街131号',
        status: 'completed'
      },
      '1003': {
        id: '1003',
        appointmentTime: '2025-06-28 10:00-11:00',
        specialty: '儿童口腔',
        doctorName: '周医生',
        clinicAddress: '北京市东城区东直门外大街48号东方银座',
        status: 'cancelled'
      }
    };
    
    if (mockAppointments[appointmentId]) {
      appointmentDetail.value = mockAppointments[appointmentId];
    } else {
      // 如果没有找到预约，使用路由参数
      const eventChannel = this.getOpenerEventChannel();
      eventChannel.on('appointmentDetail', (data) => {
        appointmentDetail.value = data;
      });
    }
  } else {
    // 如果没有appointmentId，尝试从页面参数获取
    const eventChannel = this.getOpenerEventChannel();
    eventChannel.on('appointmentDetail', (data) => {
      appointmentDetail.value = data;
    });
  }
});
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #a8c0ff;
  background-image: linear-gradient(135deg, #a8c0ff 0%, #8e99f3 100%);
}

.user-info {
  margin-bottom: 30rpx;
  text-align: center;
}

.phone-number {
  font-size: 32rpx;
  color: #fff;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 15rpx 30rpx;
  border-radius: 30rpx;
  margin-bottom: 20rpx;
  display: inline-block;
}

.reminder-section {
  margin-bottom: 30rpx;
}

.reminder-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 15rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.reminder-content {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 20rpx;
  border-radius: 16rpx;
  border-left: 6rpx solid rgba(255, 255, 255, 0.5);
}

.detail-card {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.detail-item {
  display: flex;
  margin-bottom: 25rpx;
  align-items: flex-start;
}

.detail-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
}

.detail-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  word-break: break-all;
}

.time-value {
  color: #4a90e2;
  font-weight: 600;
}

.appointment-status {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-upcoming {
  background-color: rgba(74, 144, 226, 0.1);
  color: #4a90e2;
  border: 1rpx solid #4a90e2;
}

.status-completed {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  border: 1rpx solid #4caf50;
}

.status-cancelled {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  border: 1rpx solid #f44336;
}

.btn-section {
  margin-top: auto;
  padding: 20rpx 0;
}

.cancel-btn {
  width: 100%;
  height: 90rpx;
  background-color: #f44336;
  color: #ffffff;
  font-size: 32rpx;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(244, 67, 54, 0.3);
  
  &:active {
    background-color: #e53935;
    transform: scale(0.98);
  }
}

.reappoint-btn {
  width: 100%;
  height: 90rpx;
  background-color: #4caf50;
  color: #ffffff;
  font-size: 32rpx;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
  
  &:active {
    background-color: #43a047;
    transform: scale(0.98);
  }
}

.return-btn {
  width: 100%;
  height: 90rpx;
  background-color: #8e99f3;
  color: #ffffff;
  font-size: 32rpx;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(142, 153, 243, 0.3);
  
  &:active {
    background-color: #7a87e6;
    transform: scale(0.98);
  }
}
</style> 