# 通知相关接口

## 基础信息
- **基础URL**: `https://api.dentalcare.com/v1`
- **认证方式**: JWT <PERSON> (Bearer Authentication)
- **请求头**: `Authorization: Bearer {token}`

## 1. 通知获取

### 1.1 获取用户通知列表
- **URL**: `/notifications`
- **方法**: `GET`
- **描述**: 获取当前用户的通知列表
- **查询参数**:
  - `type`: 通知类型（appointment/system/rating/reminder）
  - `status`: 状态（unread/read/all）
  - `page`: 页码
  - `limit`: 每页数量

- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "notifications": [
      {
        "notificationId": "not001",
        "type": "appointment",
        "title": "预约确认通知",
        "content": "您的预约已被李医生确认，预约时间：2023-11-20 14:00",
        "status": "unread",
        "priority": "high",
        "relatedId": "apt001",
        "relatedType": "appointment",
        "actionUrl": "/pages/appointment-detail/index?id=apt001",
        "createdAt": "2023-11-15T10:00:00Z",
        "readAt": null,
        "metadata": {
          "doctorName": "李医生",
          "appointmentTime": "2023-11-20T14:00:00Z"
        }
      },
      {
        "notificationId": "not002",
        "type": "reminder",
        "title": "预约提醒",
        "content": "您明天14:00有预约，请准时到达",
        "status": "read",
        "priority": "medium",
        "relatedId": "apt001",
        "relatedType": "appointment",
        "createdAt": "2023-11-19T14:00:00Z",
        "readAt": "2023-11-19T15:30:00Z"
      }
    ],
    "summary": {
      "unreadCount": 5,
      "totalCount": 25
    },
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    }
  }
}
```

### 1.2 获取通知详情
- **URL**: `/notifications/{notificationId}`
- **方法**: `GET`
- **描述**: 获取指定通知的详细信息
- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "notificationId": "not001",
    "type": "appointment",
    "title": "预约确认通知",
    "content": "您的预约已被李医生确认，预约时间：2023-11-20 14:00",
    "status": "unread",
    "priority": "high",
    "relatedId": "apt001",
    "relatedType": "appointment",
    "actionUrl": "/pages/appointment-detail/index?id=apt001",
    "createdAt": "2023-11-15T10:00:00Z",
    "metadata": {
      "doctorName": "李医生",
      "appointmentTime": "2023-11-20T14:00:00Z",
      "hospital": "北京口腔医院"
    }
  }
}
```

### 1.3 获取未读通知数量
- **URL**: `/notifications/unread-count`
- **方法**: `GET`
- **描述**: 获取用户未读通知数量
- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "unreadCount": 5,
    "typeBreakdown": {
      "appointment": 2,
      "system": 1,
      "rating": 1,
      "reminder": 1
    }
  }
}
```

## 2. 通知状态管理

### 2.1 标记通知为已读
- **URL**: `/notifications/{notificationId}/read`
- **方法**: `PUT`
- **描述**: 标记指定通知为已读
- **成功响应**:
```json
{
  "code": 200,
  "message": "标记成功",
  "data": {
    "notificationId": "not001",
    "status": "read",
    "readAt": "2023-11-16T10:30:00Z"
  }
}
```

### 2.2 批量标记通知为已读
- **URL**: `/notifications/batch-read`
- **方法**: `PUT`
- **描述**: 批量标记通知为已读
- **请求参数**:
```json
{
  "notificationIds": ["not001", "not002", "not003"],
  "markAll": false
}
```

- **成功响应**:
```json
{
  "code": 200,
  "message": "批量标记成功",
  "data": {
    "markedCount": 3,
    "failedIds": []
  }
}
```

### 2.3 标记所有通知为已读
- **URL**: `/notifications/read-all`
- **方法**: `PUT`
- **描述**: 标记所有通知为已读
- **请求参数**:
```json
{
  "type": "appointment"
}
```

### 2.4 删除通知
- **URL**: `/notifications/{notificationId}`
- **方法**: `DELETE`
- **描述**: 删除指定通知

## 3. 通知设置

### 3.1 获取通知设置
- **URL**: `/notifications/settings`
- **方法**: `GET`
- **描述**: 获取用户的通知设置
- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "pushNotifications": {
      "enabled": true,
      "appointment": true,
      "reminder": true,
      "rating": false,
      "system": true
    },
    "smsNotifications": {
      "enabled": true,
      "appointment": true,
      "reminder": true,
      "rating": false,
      "system": false
    },
    "emailNotifications": {
      "enabled": false,
      "appointment": false,
      "reminder": false,
      "rating": false,
      "system": false
    },
    "reminderSettings": {
      "appointmentReminders": ["24_hours", "1_hour"],
      "quietHours": {
        "enabled": true,
        "startTime": "22:00",
        "endTime": "08:00"
      }
    }
  }
}
```

### 3.2 更新通知设置
- **URL**: `/notifications/settings`
- **方法**: `PUT`
- **描述**: 更新用户的通知设置
- **请求参数**:
```json
{
  "pushNotifications": {
    "enabled": true,
    "appointment": true,
    "reminder": true,
    "rating": false,
    "system": true
  },
  "smsNotifications": {
    "enabled": true,
    "appointment": true,
    "reminder": true,
    "rating": false,
    "system": false
  },
  "reminderSettings": {
    "appointmentReminders": ["24_hours", "1_hour"],
    "quietHours": {
      "enabled": true,
      "startTime": "22:00",
      "endTime": "08:00"
    }
  }
}
```

## 4. 推送通知

### 4.1 注册推送设备
- **URL**: `/notifications/devices`
- **方法**: `POST`
- **描述**: 注册用户设备以接收推送通知
- **请求参数**:
```json
{
  "deviceToken": "device_token_string",
  "platform": "ios",
  "appVersion": "1.0.0",
  "deviceModel": "iPhone 14",
  "osVersion": "16.0"
}
```

### 4.2 更新设备信息
- **URL**: `/notifications/devices/{deviceId}`
- **方法**: `PUT`
- **描述**: 更新设备信息

### 4.3 注销设备
- **URL**: `/notifications/devices/{deviceId}`
- **方法**: `DELETE`
- **描述**: 注销设备，停止接收推送通知

## 5. 预约提醒

### 5.1 设置预约提醒
- **URL**: `/notifications/appointment-reminders`
- **方法**: `POST`
- **描述**: 为预约设置提醒
- **请求参数**:
```json
{
  "appointmentId": "apt001",
  "reminders": [
    {
      "type": "push",
      "timing": "24_hours_before",
      "enabled": true
    },
    {
      "type": "sms",
      "timing": "1_hour_before",
      "enabled": true
    }
  ]
}
```

### 5.2 获取预约提醒设置
- **URL**: `/notifications/appointment-reminders/{appointmentId}`
- **方法**: `GET`
- **描述**: 获取指定预约的提醒设置

### 5.3 更新预约提醒设置
- **URL**: `/notifications/appointment-reminders/{appointmentId}`
- **方法**: `PUT`
- **描述**: 更新预约提醒设置

## 6. 系统通知

### 6.1 发送系统通知（管理员）
- **URL**: `/admin/notifications/broadcast`
- **方法**: `POST`
- **描述**: 管理员发送系统广播通知
- **请求参数**:
```json
{
  "title": "系统维护通知",
  "content": "系统将于今晚22:00-24:00进行维护，期间可能影响服务",
  "type": "system",
  "priority": "high",
  "targetUsers": {
    "userType": "all",
    "userIds": [],
    "criteria": {}
  },
  "channels": ["push", "sms"],
  "scheduledAt": "2023-11-16T20:00:00Z"
}
```

### 6.2 获取系统通知模板
- **URL**: `/admin/notifications/templates`
- **方法**: `GET`
- **描述**: 获取系统通知模板列表

## 7. 通知统计

### 7.1 获取通知统计
- **URL**: `/admin/notifications/statistics`
- **方法**: `GET`
- **描述**: 获取通知发送统计数据
- **查询参数**:
  - `startDate`: 开始日期
  - `endDate`: 结束日期
  - `type`: 通知类型

- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "totalSent": 15680,
    "totalDelivered": 14520,
    "totalRead": 12350,
    "deliveryRate": 92.6,
    "readRate": 78.8,
    "typeBreakdown": {
      "appointment": 8500,
      "reminder": 4200,
      "system": 1980,
      "rating": 1000
    },
    "channelBreakdown": {
      "push": 12000,
      "sms": 2680,
      "email": 1000
    }
  }
}
```
