<template>
  <view class="container">
    <!-- 标题 -->
    <view class="page-header">
      <text class="page-title">请确认你的信息</text>
    </view>
    
    <!-- 医生信息 -->
    <view class="doctor-info-card">
      <view class="doctor-name">{{ selectedDoctor.name }}</view>
      <view class="doctor-address">{{ selectedDoctor.address }}</view>
    </view>
    
    <!-- 口腔健康状况卡片 -->
    <DentalHealthCard v-if="dentalRecord" :record="dentalRecord" />
    <view v-else class="loading-card">
      <text>正在加载口腔健康数据...</text>
    </view>
    
    <!-- 日期选择 -->
    <view class="section-title">选择日期</view>
    <scroll-view class="date-scroll" scroll-x show-scrollbar="false">
      <view class="date-list">
        <view 
          v-for="(date, index) in availableDates" 
          :key="index"
          class="date-item"
          :class="{ active: selectedDate === date.value }"
          @tap="selectDate(date.value)"
        >
          <text class="day">{{ date.day }}</text>
          <text class="date">{{ date.date }}</text>
          <text class="month">{{ date.month }}</text>
        </view>
      </view>
    </scroll-view>
    
    <!-- 时间段选择 -->
    <view class="section-title">选择时间</view>
    <view class="time-section">
      <view class="period-title">上午</view>
      <view class="time-grid">
        <view 
          v-for="(time, index) in morningTimes" 
          :key="index"
          class="time-item"
          :class="{ 
            active: selectedTimes.includes(time.value),
            disabled: isTimeDisabled(time.value),
            adjacent: isAdjacentToSelected(time.value) && !selectedTimes.includes(time.value) && selectedTimes.length > 0
          }"
          @tap="toggleTimeSelection(time.value)"
        >
          {{ time.label }}
        </view>
      </view>
    </view>
    
    <view class="time-section">
      <view class="period-title">下午</view>
      <view class="time-grid">
        <view 
          v-for="(time, index) in afternoonTimes" 
          :key="index"
          class="time-item"
          :class="{ 
            active: selectedTimes.includes(time.value),
            disabled: isTimeDisabled(time.value),
            adjacent: isAdjacentToSelected(time.value) && !selectedTimes.includes(time.value) && selectedTimes.length > 0
          }"
          @tap="toggleTimeSelection(time.value)"
        >
          {{ time.label }}
        </view>
      </view>
    </view>
    
    <!-- 预约说明 -->
    <view class="booking-notes">
      <text>预约说明：</text>
      <text>1. 每次预约最多可选择2个相邻时间段</text>
      <text>2. 上午和下午时间段不可同时选择</text>
    </view>
    
    <!-- 确认按钮 -->
    <view class="btn-section">
      <button class="confirm-btn" @tap="confirmAppointment">确认预约</button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import DentalHealthCard from '@/components/DentalHealthCard.vue';
import dentalMock from '@/mock/dental';

// 获取选中的医生信息
const selectedDoctor = ref({
  id: 0,
  name: '加载中...',
  address: '加载中...'
});

// 口腔健康记录
const dentalRecord = ref(null);

// 日期相关
const today = new Date();
const availableDates = ref([]);
const selectedDate = ref('');

// 时间段
const morningTimes = [
  { label: '08:00-08:30', value: 'morning_1' },
  { label: '08:30-09:00', value: 'morning_2' },
  { label: '09:00-09:30', value: 'morning_3' },
  { label: '09:30-10:00', value: 'morning_4' },
  { label: '10:00-10:30', value: 'morning_5' },
  { label: '10:30-11:00', value: 'morning_6' },
  { label: '11:00-11:30', value: 'morning_7' },
  { label: '11:30-12:00', value: 'morning_8' }
];

const afternoonTimes = [
  { label: '14:00-14:30', value: 'afternoon_1' },
  { label: '14:30-15:00', value: 'afternoon_2' },
  { label: '15:00-15:30', value: 'afternoon_3' },
  { label: '15:30-16:00', value: 'afternoon_4' },
  { label: '16:00-16:30', value: 'afternoon_5' },
  { label: '16:30-17:00', value: 'afternoon_6' },
  { label: '17:00-17:30', value: 'afternoon_7' },
  { label: '17:30-18:00', value: 'afternoon_8' }
];

const selectedTimes = ref([]);

// 周几
const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];

// 生成未来7天的日期
const generateDates = () => {
  const dates = [];
  for (let i = 0; i < 7; i++) {
    const date = new Date();
    date.setDate(today.getDate() + i);
    
    const day = weekDays[date.getDay()];
    const dateNum = date.getDate();
    const month = `${date.getMonth() + 1}月`;
    const dateValue = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(dateNum).padStart(2, '0')}`;
    
    dates.push({
      day,
      date: dateNum,
      month,
      value: dateValue
    });
  }
  return dates;
};

// 判断时间段是否禁用
const isTimeDisabled = (timeValue) => {
  // 如果没有选择，则不禁用任何时间段
  if (selectedTimes.value.length === 0) return false;
  
  // 检查是否已选择了上午或下午的时间段
  const isPeriodMorning = timeValue.startsWith('morning');
  const hasSelectedMorning = selectedTimes.value.some(t => t.startsWith('morning'));
  const hasSelectedAfternoon = selectedTimes.value.some(t => t.startsWith('afternoon'));
  
  // 如果选择了上午，则下午禁用；如果选择了下午，则上午禁用
  if ((isPeriodMorning && hasSelectedAfternoon) || (!isPeriodMorning && hasSelectedMorning)) {
    return true;
  }
  
  // 如果已选择了时间段，则只有相邻的时间段可选，其他都禁用
  return !isAdjacentToSelected(timeValue) && !selectedTimes.value.includes(timeValue);
};

// 判断时间段是否与已选择的时间段相邻
const isAdjacentToSelected = (timeValue) => {
  if (selectedTimes.value.length === 0) return true;
  
  const isPeriodMorning = timeValue.startsWith('morning');
  const timeIndex = isPeriodMorning 
    ? parseInt(timeValue.split('_')[1]) 
    : parseInt(timeValue.split('_')[1]);
  
  // 检查是否与已选择的时间段相邻
  return selectedTimes.value.some(selectedTime => {
    const selectedIsMorning = selectedTime.startsWith('morning');
    const selectedIndex = selectedIsMorning 
      ? parseInt(selectedTime.split('_')[1]) 
      : parseInt(selectedTime.split('_')[1]);
    
    // 只有同一时段（上午或下午）的时间才能相邻
    if (isPeriodMorning !== selectedIsMorning) return false;
    
    // 相邻意味着索引差为1
    return Math.abs(timeIndex - selectedIndex) === 1;
  });
};

// 选择或取消选择时间段
const toggleTimeSelection = (timeValue) => {
  // 如果时间段被禁用，则不允许选择
  if (isTimeDisabled(timeValue)) return;
  
  // 如果已经选择了该时间段，则取消选择
  if (selectedTimes.value.includes(timeValue)) {
    selectedTimes.value = selectedTimes.value.filter(t => t !== timeValue);
    return;
  }
  
  // 如果已选择了2个时间段，则替换最早选择的一个
  if (selectedTimes.value.length === 2) {
    selectedTimes.value.shift(); // 移除第一个选择的时间段
  }
  
  // 添加新选择的时间段
  selectedTimes.value.push(timeValue);
};

// 选择日期
const selectDate = (date) => {
  selectedDate.value = date;
  // 重置已选择的时间段
  selectedTimes.value = [];
};

// 获取口腔健康数据
const fetchDentalRecord = () => {
  try {
    // 由于API连接问题，直接使用模拟数据
    const res = dentalMock.getLatestDentalRecord();
    if (res.success && res.record) {
      dentalRecord.value = res.record;
    } else {
      console.error('获取口腔健康数据失败', res);
    }
  } catch (error) {
    console.error('获取口腔健康数据错误', error);
  }
};

// 确认预约
const confirmAppointment = () => {
  if (!selectedDate.value) {
    uni.showToast({
      title: '请选择预约日期',
      icon: 'none'
    });
    return;
  }
  
  if (selectedTimes.value.length === 0) {
    uni.showToast({
      title: '请选择预约时间段',
      icon: 'none'
    });
    return;
  }
  
  // 显示加载中
  uni.showLoading({
    title: '预约中...'
  });
  
  // 模拟预约请求
  setTimeout(() => {
    uni.hideLoading();
    
    // 随机成功或失败（演示用）
    const isSuccess = Math.random() > 0.2;
    
    if (isSuccess) {
      uni.showToast({
        title: '预约成功',
        icon: 'success'
      });
      
      // 保存预约信息
      const appointmentInfo = {
        doctorId: selectedDoctor.value.id,
        doctorName: selectedDoctor.value.name,
        date: selectedDate.value,
        times: selectedTimes.value
      };
      uni.setStorageSync('appointmentInfo', appointmentInfo);
      
      // 延迟跳转到用户中心页面
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/patient-dashboard/index'
        });
      }, 1500);
    } else {
      uni.showToast({
        title: '预约失败，请重试',
        icon: 'error'
      });
    }
  }, 2000);
};

// 页面加载时
onMounted(() => {
  // 获取选中的医生信息
  try {
    const doctor = uni.getStorageSync('selectedDoctor');
    if (doctor) {
      selectedDoctor.value = doctor;
    }
  } catch (e) {
    console.error('获取医生信息失败', e);
  }
  
  // 生成可选日期
  availableDates.value = generateDates();
  // 默认选择今天
  selectedDate.value = availableDates.value[0].value;
  
  // 获取口腔健康数据
  fetchDentalRecord();
});
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #a8c0ff;
  background-image: linear-gradient(135deg, #a8c0ff 0%, #8e99f3 100%);
}

.page-header {
  margin-bottom: 20rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #4a5b8c;
  text-align: center;
  display: block;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 20rpx 0;
  border-radius: 10rpx;
  margin: 0 auto;
  width: 80%;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(168, 192, 255, 0.3);
}

.doctor-info-card {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.doctor-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 15rpx;
}

.doctor-address {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.loading-card {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 20rpx;
  margin: 0 auto 20rpx;
  width: 94%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  height: 160rpx;
  font-size: 26rpx;
  color: #999;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #fff;
  margin: 20rpx 0;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.date-scroll {
  width: 100%;
  white-space: nowrap;
  margin-bottom: 30rpx;
}

.date-list {
  display: flex;
  padding: 10rpx 0;
}

.date-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 160rpx;
  background-color: rgba(255, 255, 255, 0.8);
  margin-right: 20rpx;
  border-radius: 16rpx;
  padding: 15rpx 0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &.active {
    background-color: #8e99f3;
    transform: scale(1.05);
    box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.15);
    
    .day, .date, .month {
      color: #fff;
    }
  }
}

.day {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.date {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 5rpx;
}

.month {
  font-size: 24rpx;
  color: #666;
}

.time-section {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.period-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.time-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.time-item {
  width: 48%;
  height: 80rpx;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(142, 153, 243, 0.3);
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 15rpx;
  transition: all 0.3s ease;
  
  &.active {
    background-color: #8e99f3;
    color: #fff;
    border-color: #8e99f3;
    transform: scale(1.03);
    box-shadow: 0 2rpx 8rpx rgba(142, 153, 243, 0.5);
  }
  
  &.disabled {
    background-color: rgba(0, 0, 0, 0.05);
    color: #999;
    border-color: transparent;
    pointer-events: none;
  }
  
  &.adjacent {
    background-color: rgba(255, 255, 255, 0.8);
    border-color: rgba(142, 153, 243, 0.5);
    box-shadow: 0 2rpx 8rpx rgba(142, 153, 243, 0.2);
  }
}

.booking-notes {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 16rpx;
  padding: 20rpx;
  margin: 20rpx 0 30rpx;
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  
  text {
    display: block;
  }
}

.btn-section {
  margin-top: auto;
  padding: 20rpx 0;
}

.confirm-btn {
  width: 100%;
  height: 90rpx;
  background-color: #8e99f3;
  color: #ffffff;
  font-size: 32rpx;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(142, 153, 243, 0.3);
  
  &:active {
    background-color: #7a87e6;
    transform: scale(0.98);
  }
}
</style> 