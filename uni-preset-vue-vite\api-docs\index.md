# 口腔健康预约系统技术文档

## 系统概述

口腔健康预约系统是一款移动端APP，旨在为患者提供便捷的口腔医疗服务预约体验，同时为医生提供高效的门诊管理工具。系统支持三种用户角色：患者、医生和管理员，每种角色具有不同的功能权限。

### 主要功能

#### 患者端功能
- 智能推荐牙科医生（基于位置和专科）
- 门诊预约（选择医生空闲时间）
- 就医提醒（提前1小时和1天通知）
- 医生评价（五星级打分系统）

#### 医生端功能
- 管理开放空闲时间
- 接收门诊提醒

#### 管理员功能
- 管理用户账号（患者和医生）
- 查看系统统计数据

### 技术架构

本系统采用前后端分离架构：
- 前端：移动端APP（基于uni-app框架开发）
- 后端：RESTful API服务
- 数据库：关系型数据库（MySQL/PostgreSQL）

## 文档导航

本文档集包含以下内容：

### [1. API接口规范](./README.md)
详细的后端API接口说明，包括：
- 用户认证相关接口
- 患者相关接口
- 医生相关接口
- 管理员相关接口
- 预约相关接口
- 评价相关接口
- 通知相关接口

### [2. 数据库设计](./数据库设计.md)
系统数据库表结构设计，包括：
- 数据库选型建议
- 表结构详细设计
- 索引设计
- 数据库关系图
- 数据库优化建议

### [3. 开发指南](./开发指南.md)
前后端交互流程和开发规范，包括：
- 前后端交互流程
- API请求规范
- 前端开发规范
- 后端开发规范
- 关键功能实现指南
- 测试指南
- 部署指南
- 性能优化建议

## 系统流程图

### 用户注册登录流程

```
+-------------+      +---------------+      +---------------+
| 输入账号信息 | ---> | 选择用户类型  | ---> | 提交注册信息  |
+-------------+      +---------------+      +---------------+
                                                   |
                                                   v
+-------------+      +---------------+      +---------------+
| 进入对应页面 | <--- | 获取用户信息  | <--- | 服务器验证   |
+-------------+      +---------------+      +---------------+
```

### 预约流程

```
+-------------+      +---------------+      +---------------+
| 搜索推荐医生 | ---> | 查看医生详情  | ---> | 选择预约时间  |
+-------------+      +---------------+      +---------------+
                                                   |
                                                   v
+-------------+      +---------------+      +---------------+
| 收到预约提醒 | <--- | 预约成功通知  | <--- | 确认预约信息  |
+-------------+      +---------------+      +---------------+
```

## 开发与部署时间线

1. **需求分析与设计阶段**：1-2周
   - 需求收集与分析
   - 系统架构设计
   - 数据库设计
   - API接口设计

2. **开发阶段**：4-6周
   - 后端API开发：2-3周
   - 前端APP开发：2-3周
   - 集成测试：1周

3. **测试阶段**：1-2周
   - 功能测试
   - 性能测试
   - 用户体验测试

4. **部署与上线**：1周
   - 服务器环境配置
   - 数据库部署
   - 应用部署
   - 上线前最终测试

## 注意事项与建议

1. **安全性**
   - 所有API请求应使用HTTPS
   - 敏感数据（如密码）必须加密存储
   - 实施适当的访问控制和权限管理

2. **性能**
   - 对于地理位置查询，建议使用空间索引优化
   - 实施缓存策略，减少数据库负载
   - 考虑使用CDN加速静态资源

3. **可扩展性**
   - 设计时考虑未来功能扩展
   - 采用模块化架构，便于维护和升级

4. **用户体验**
   - 确保预约流程简单直观
   - 提供及时的通知和提醒
   - 优化移动端页面加载速度 