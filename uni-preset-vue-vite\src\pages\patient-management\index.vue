<template>
  <view class="container">
    <!-- 顶部标题 -->
    <view class="header">
      <text class="title">管理用户账号</text>
    </view>
    
    <!-- 搜索框 -->
    <view class="search-container">
      <input class="search-input" v-model="searchQuery" placeholder="搜索用户姓名或手机号" @input="handleSearch" />
      <text class="search-icon">🔍</text>
    </view>
    
    <!-- 用户列表 -->
    <scroll-view class="patient-list" scroll-y v-if="filteredPatients.length > 0">
      <view v-for="(patient, index) in filteredPatients" :key="index" class="patient-item">
        <view v-if="filteredPatients.length === 0" class="empty-list">
          <text class="empty-text">暂无用户信息</text>
        </view>
        <view class="patient-info">
          <text class="patient-name">{{ patient.name }}</text>
          <text class="patient-phone">{{ patient.phone }}</text>
        </view>
        <view class="patient-actions">
          <button class="action-btn edit-btn" @tap="editPatient(patient)">编辑</button>
          <button class="action-btn delete-btn" @tap="deletePatient(patient)">删除</button>
        </view>
      </view>
    </scroll-view>
    
    <view v-else class="empty-list">
      <text class="empty-text">暂无用户信息</text>
    </view>
    

  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 用户列表
const patientList = ref([
  {
    id: 'P10001',
    name: '张三',
    phone: '13800138000',
    gender: '男',
    age: 28,
    address: '北京市朝阳区健康路1号'
  },
  {
    id: 'P10002',
    name: '李四',
    phone: '13900139000',
    gender: '女',
    age: 35,
    address: '上海市浦东新区陆家嘴金融中心'
  },
  {
    id: 'P10003',
    name: '王五',
    phone: '13700137000',
    gender: '男',
    age: 42,
    address: '广州市天河区体育西路123号'
  }
]);

// 搜索关键词
const searchQuery = ref('');

// 筛选后的用户列表
const filteredPatients = computed(() => {
  if (!searchQuery.value) {
    return patientList.value;
  }
  
  const query = searchQuery.value.toLowerCase();
  return patientList.value.filter(patient => 
    patient.name.toLowerCase().includes(query) || 
    patient.phone.includes(query)
  );
});

// 获取用户列表
const fetchPatientList = async () => {
  // 实际项目中应该从API获取数据
  // 这里使用模拟数据
};



// 编辑用户
const editPatient = (patient) => {
  // 保存当前选中的用户信息
  try {
    uni.setStorageSync('editPatientInfo', patient);
    uni.navigateTo({ 
      url: '/pages/patient-form/index?mode=edit&id=' + patient.id 
    });
  } catch (e) {
    console.error('保存用户信息失败', e);
    uni.showToast({ title: '操作失败', icon: 'none' });
  }
};

// 删除用户
const deletePatient = (patient) => {
  uni.showModal({
    title: '确认删除',
    content: `确定要删除用户 ${patient.name}(${patient.phone}) 的账号吗？此操作不可恢复。`,
    success: (res) => {
      if (res.confirm) {
        // 实际应用中应该调用API删除用户
        patientList.value = patientList.value.filter(p => p.id !== patient.id);
        uni.showToast({ title: '删除成功', icon: 'success' });
      }
    }
  });
};

// 处理搜索
const handleSearch = () => {
  // 搜索逻辑已通过计算属性实现
};

onMounted(() => {
  fetchPatientList();
});
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #f5f5f5;
}

.header {
  margin-bottom: 30rpx;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
}

.search-container {
  position: relative;
  margin-bottom: 30rpx;
}

.search-input {
  width: 100%;
  height: 80rpx;
  padding: 0 80rpx 0 30rpx;
  border-radius: 40rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  font-size: 28rpx;
}

.search-icon {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
}

/* 用户列表样式 */
.patient-list {
  flex: 1;
  margin-bottom: 30rpx;
}

.patient-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.patient-info {
  flex: 1;
}

.patient-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.patient-phone {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.patient-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  padding: 0 30rpx;
  height: 60rpx;
  line-height: 60rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  background-color: transparent;
}

.edit-btn {
  color: #4a56e2;
  border: 1px solid #4a56e2;
}

.delete-btn {
  color: #ff4d4f;
  border: 1px solid #ff4d4f;
}

.empty-list {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
  background-color: #fff;
  border-radius: 16rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

.add-container {
  margin-top: auto;
  padding: 30rpx 0;
}

.add-button {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background: linear-gradient(135deg, #8e99f3 0%, #4a56e2 100%);
  color: #fff;
  border-radius: 45rpx;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(74, 86, 226, 0.3);
}
</style> 