# 口腔健康预约系统接口测试指南

## 测试环境准备

### 1. 环境配置

测试环境基础URL：`https://test-api.dentalcare.com/v1`

### 2. 测试工具

推荐使用以下工具进行API测试：
- [Postman](https://www.postman.com/)：GUI接口测试工具
- [Jest](https://jestjs.io/) + [Supertest](https://github.com/visionmedia/supertest)：自动化接口测试
- [curl](https://curl.se/)：命令行测试工具

### 3. 测试账号

测试环境已预置以下测试账号：

| 角色 | 账号 | 密码 | 说明 |
| --- | --- | --- | --- |
| 患者 | <EMAIL> | test123 | 普通患者账号 |
| 医生 | <EMAIL> | test123 | 牙齿矫正专科医生 |
| 管理员 | <EMAIL> | admin123 | 系统管理员 |

## 测试流程

### 1. 用户认证测试

#### 1.1 用户登录测试

**请求示例（Postman）**：
```
POST https://test-api.dentalcare.com/v1/auth/login
Content-Type: application/json

{
  "account": "<EMAIL>",
  "password": "test123",
  "userType": "patient"
}
```

**请求示例（curl）**：
```bash
curl -X POST https://test-api.dentalcare.com/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "account": "<EMAIL>",
    "password": "test123",
    "userType": "patient"
  }'
```

**预期结果**：
- 状态码：200
- 返回包含用户信息和token的JSON数据
- 记录返回的token用于后续接口测试

#### 1.2 获取用户信息测试

**请求示例（Postman）**：
```
GET https://test-api.dentalcare.com/v1/auth/user-info
Authorization: Bearer {token}
```

**请求示例（curl）**：
```bash
curl -X GET https://test-api.dentalcare.com/v1/auth/user-info \
  -H "Authorization: Bearer {token}"
```

**预期结果**：
- 状态码：200
- 返回包含用户详细信息的JSON数据

### 2. 患者功能测试

#### 2.1 更新患者位置测试

**请求示例（Postman）**：
```
POST https://test-api.dentalcare.com/v1/patient/location
Authorization: Bearer {token}
Content-Type: application/json

{
  "latitude": 39.9042,
  "longitude": 116.4074,
  "address": "北京市海淀区"
}
```

**预期结果**：
- 状态码：200
- 返回成功消息

#### 2.2 获取推荐医生测试

**请求示例（Postman）**：
```
GET https://test-api.dentalcare.com/v1/patient/recommended-doctors?specialty=牙齿矫正&maxDistance=10
Authorization: Bearer {token}
```

**预期结果**：
- 状态码：200
- 返回医生列表数据，包含距离信息

### 3. 医生功能测试

#### 3.1 添加医生空闲时间测试

**请求示例（Postman）**：
```
POST https://test-api.dentalcare.com/v1/doctor/available-slots
Authorization: Bearer {token}
Content-Type: application/json

{
  "date": "2023-12-01",
  "slots": [
    {
      "startTime": "09:00",
      "endTime": "09:30"
    },
    {
      "startTime": "09:30",
      "endTime": "10:00"
    }
  ]
}
```

**预期结果**：
- 状态码：200
- 返回成功消息

#### 3.2 获取医生空闲时间测试

**请求示例（Postman）**：
```
GET https://test-api.dentalcare.com/v1/doctor/available-slots?startDate=2023-12-01&endDate=2023-12-07
Authorization: Bearer {token}
```

**预期结果**：
- 状态码：200
- 返回医生空闲时间列表数据

### 4. 预约功能测试

#### 4.1 创建预约测试

**请求示例（Postman）**：
```
POST https://test-api.dentalcare.com/v1/appointments
Authorization: Bearer {token}
Content-Type: application/json

{
  "doctorId": "d10001",
  "slotId": "s10001",
  "note": "测试预约"
}
```

**预期结果**：
- 状态码：200
- 返回预约成功信息
- 医生空闲时间状态更新为已预约

#### 4.2 取消预约测试

**请求示例（Postman）**：
```
PUT https://test-api.dentalcare.com/v1/appointments/a10001/cancel
Authorization: Bearer {token}
Content-Type: application/json

{
  "reason": "测试取消"
}
```

**预期结果**：
- 状态码：200
- 返回取消成功信息
- 医生空闲时间状态更新为未预约

### 5. 管理员功能测试

#### 5.1 获取用户列表测试

**请求示例（Postman）**：
```
GET https://test-api.dentalcare.com/v1/admin/users?userType=patient&page=1&pageSize=10
Authorization: Bearer {token}
```

**预期结果**：
- 状态码：200
- 返回用户列表数据

#### 5.2 修改用户信息测试

**请求示例（Postman）**：
```
PUT https://test-api.dentalcare.com/v1/admin/users/p10001
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "测试用户",
  "phone": "***********"
}
```

**预期结果**：
- 状态码：200
- 返回修改成功信息
- 用户信息被更新

## 自动化测试示例

### Jest + Supertest示例

```javascript
const request = require('supertest');
const baseURL = 'https://test-api.dentalcare.com/v1';

let token;

// 登录测试
describe('认证测试', () => {
  test('用户登录', async () => {
    const response = await request(baseURL)
      .post('/auth/login')
      .send({
        account: '<EMAIL>',
        password: 'test123',
        userType: 'patient'
      });
    
    expect(response.statusCode).toBe(200);
    expect(response.body.code).toBe(200);
    expect(response.body.data).toHaveProperty('token');
    
    // 保存token用于后续测试
    token = response.body.data.token;
  });
  
  test('获取用户信息', async () => {
    const response = await request(baseURL)
      .get('/auth/user-info')
      .set('Authorization', `Bearer ${token}`);
    
    expect(response.statusCode).toBe(200);
    expect(response.body.code).toBe(200);
    expect(response.body.data).toHaveProperty('userId');
  });
});

// 预约流程测试
describe('预约流程测试', () => {
  let doctorId;
  let slotId;
  let appointmentId;
  
  test('获取推荐医生', async () => {
    const response = await request(baseURL)
      .get('/patient/recommended-doctors')
      .set('Authorization', `Bearer ${token}`);
    
    expect(response.statusCode).toBe(200);
    expect(response.body.data.list.length).toBeGreaterThan(0);
    
    // 保存第一个医生ID用于后续测试
    doctorId = response.body.data.list[0].doctorId;
  });
  
  test('获取医生空闲时间', async () => {
    const response = await request(baseURL)
      .get(`/doctor/available-slots?doctorId=${doctorId}`)
      .set('Authorization', `Bearer ${token}`);
    
    expect(response.statusCode).toBe(200);
    expect(response.body.data.length).toBeGreaterThan(0);
    
    // 找到第一个未被预约的时间段
    const availableSlot = response.body.data[0].slots.find(slot => !slot.isBooked);
    expect(availableSlot).toBeDefined();
    
    // 保存时间段ID用于后续测试
    slotId = availableSlot.slotId;
  });
  
  test('创建预约', async () => {
    const response = await request(baseURL)
      .post('/appointments')
      .set('Authorization', `Bearer ${token}`)
      .send({
        doctorId,
        slotId,
        note: '自动化测试预约'
      });
    
    expect(response.statusCode).toBe(200);
    expect(response.body.data).toHaveProperty('appointmentId');
    
    // 保存预约ID用于后续测试
    appointmentId = response.body.data.appointmentId;
  });
  
  test('取消预约', async () => {
    const response = await request(baseURL)
      .put(`/appointments/${appointmentId}/cancel`)
      .set('Authorization', `Bearer ${token}`)
      .send({
        reason: '自动化测试取消'
      });
    
    expect(response.statusCode).toBe(200);
    expect(response.body.code).toBe(200);
  });
});
```

## 测试注意事项

1. **测试数据隔离**：
   - 使用专门的测试数据库
   - 每次测试前重置数据库状态
   - 避免测试数据相互干扰

2. **错误测试**：
   - 测试各种错误情况（如无效参数、权限不足等）
   - 验证错误响应的状态码和消息

3. **并发测试**：
   - 测试多用户同时预约同一时间段的情况
   - 验证系统的并发处理能力

4. **性能测试**：
   - 使用[Apache JMeter](https://jmeter.apache.org/)等工具进行负载测试
   - 测试系统在高并发下的响应时间和稳定性

5. **安全测试**：
   - 测试未授权访问
   - 测试跨站请求伪造(CSRF)防护
   - 测试SQL注入防护

## 常见问题排查

| 问题 | 可能原因 | 解决方法 |
| --- | --- | --- |
| 401未授权 | token过期或无效 | 重新登录获取新token |
| 403权限不足 | 用户角色无权限访问该接口 | 检查用户角色和接口权限要求 |
| 404资源不存在 | URL错误或资源ID不存在 | 检查URL和资源ID |
| 409资源冲突 | 时间段已被预约 | 选择其他未被预约的时间段 |
| 500服务器错误 | 服务器内部错误 | 检查服务器日志，联系后端开发人员 | 