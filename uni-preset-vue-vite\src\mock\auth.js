/**
 * 认证相关的模拟数据
 */

// 模拟用户数据
const mockUsers = [
  {
    userId: 'P10001',
    userType: 'patient',
    name: '张三',
    gender: '男',
    phone: '13800138000',
    email: '<EMAIL>',
    password: '123456'
  },
  {
    userId: 'D10001',
    userType: 'doctor',
    name: '李医生',
    gender: '男',
    phone: '13900139000',
    email: '<EMAIL>',
    password: '123456',
    specialization: '口腔正畸科',
    clinicLocation: {
      latitude: 39.9142,
      longitude: 116.4174,
      address: '北京市朝阳区健康路88号'
    }
  },
  {
    userId: 'A10001',
    userType: 'admin',
    name: '管理员',
    gender: '男',
    phone: '13700137000',
    email: '<EMAIL>',
    password: '123456'
  }
];

// 打印初始用户数据
console.log('初始模拟用户数据:', JSON.stringify(mockUsers, null, 2));

/**
 * 模拟登录
 * @param {Object} data - 登录信息
 * @returns {Promise} - 登录结果
 */
export const mockLogin = (data) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      console.log('模拟登录请求数据:', JSON.stringify(data, null, 2));
      console.log('当前模拟用户数据总数:', mockUsers.length);
      
      // 查找匹配的用户（必须同时匹配账号和用户类型）
      const user = mockUsers.find(u => 
        (u.phone === data.account || u.email === data.account) && 
        u.password === data.password &&
        u.userType === data.userType
      );
      
      if (user) {
        console.log('找到匹配用户:', JSON.stringify(user, null, 2));
        
        // 生成模拟Token
        const token = `mock_token_${Date.now()}_${user.userId}`;
        const expireTime = Date.now() + 24 * 60 * 60 * 1000; // 24小时后过期
        
        const response = {
          code: 200,
          message: '登录成功',
          data: {
            userId: user.userId,
            userType: user.userType,
            name: user.name,
            token: token,
            expireTime: expireTime
          }
        };
        
        console.log('登录成功响应:', JSON.stringify(response, null, 2));
        resolve(response);
      } else {
        // 检查是否存在账号但用户类型不匹配的情况
        const wrongTypeUser = mockUsers.find(u => 
          (u.phone === data.account || u.email === data.account) && 
          u.password === data.password &&
          u.userType !== data.userType
        );
        
        if (wrongTypeUser) {
          // 账号存在但选择了错误的用户类型
          const errorResponse = {
            code: 401,
            message: `该账号是${wrongTypeUser.userType === 'patient' ? '患者' : wrongTypeUser.userType === 'doctor' ? '医生' : '管理员'}类型，请选择正确的用户类型`
          };
          console.log('登录失败(类型不匹配):', JSON.stringify(errorResponse, null, 2));
          reject(errorResponse);
        } else {
          // 账号或密码错误
          const errorResponse = {
            code: 401,
            message: '账号或密码错误'
          };
          console.log('登录失败(账号密码错误):', JSON.stringify(errorResponse, null, 2));
          reject(errorResponse);
        }
      }
    }, 1000); // 模拟网络延迟
  });
};

/**
 * 模拟注册
 * @param {Object} data - 注册信息
 * @returns {Promise} - 注册结果
 */
export const mockRegister = (data) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      console.log('模拟注册请求数据:', JSON.stringify(data, null, 2));
      
      // 检查是否已存在相同手机号和相同类型的用户
      const existingUser = mockUsers.find(u => {
        // 只检查相同手机号且相同用户类型的情况
        return u.phone === data.phone && u.userType === data.userType;
      });
      
      // 检查是否已存在相同邮箱的用户（如果提供了邮箱）
      const existingEmail = data.email && data.email !== '' ? 
        mockUsers.find(u => u.email === data.email && u.email !== '') : null;
      
      if (existingUser) {
        console.log('注册失败: 该手机号已注册过相同类型的账户', JSON.stringify(existingUser, null, 2));
        reject({
          code: 400,
          message: `该手机号已注册过${data.userType === 'patient' ? '患者' : data.userType === 'doctor' ? '医生' : '管理员'}账户`
        });
      } else if (existingEmail) {
        console.log('注册失败: 该邮箱已被注册', JSON.stringify(existingEmail, null, 2));
        reject({
          code: 400,
          message: '该邮箱已被注册'
        });
      } else {
        // 生成新用户ID
        const userId = data.userType === 'patient' ? 
          `P${10000 + mockUsers.length + 1}` : 
          data.userType === 'doctor' ? 
            `D${10000 + mockUsers.length + 1}` : 
            `A${10000 + mockUsers.length + 1}`;
        
        // 创建新用户对象，确保字段名称与登录一致
        const newUser = {
          userId,
          userType: data.userType,
          name: data.userType === 'admin' ? '管理员' : (data.name || '新用户'),
          gender: data.gender || '未知',
          phone: data.phone,
          email: data.email || '',
          password: data.password
        };
        
        // 如果是医生，添加医生特有字段
        if (data.userType === 'doctor') {
          newUser.specialization = '未设置';
          newUser.clinicLocation = {
            latitude: 0,
            longitude: 0,
            address: '未设置'
          };
        }
        
        // 添加新用户到模拟数据库
        mockUsers.push(newUser);
        
        console.log('新用户已添加:', JSON.stringify(newUser, null, 2));
        console.log('当前模拟用户数据总数:', mockUsers.length);
        
        const response = {
          code: 200,
          message: '注册成功',
          data: {
            userId,
            userType: data.userType,
            name: newUser.name
          }
        };
        
        console.log('注册成功响应:', JSON.stringify(response, null, 2));
        resolve(response);
      }
    }, 1000); // 模拟网络延迟
  });
};

/**
 * 模拟退出登录
 * @returns {Promise} - 退出登录结果
 */
export const mockLogout = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        message: '退出成功'
      });
    }, 500); // 模拟网络延迟
  });
};

export default {
  mockLogin,
  mockRegister,
  mockLogout
}; 