# 医生相关接口

## 基础信息
- **基础URL**: `https://api.dentalcare.com/v1`
- **认证方式**: JWT <PERSON> (Bearer Authentication)
- **请求头**: `Authorization: Bearer {token}`

## 1. 医生信息管理

### 1.1 获取医生个人信息
- **URL**: `/doctors/{doctorId}`
- **方法**: `GET`
- **描述**: 获取医生个人详细信息
- **路径参数**:
  - `doctorId`: 医生ID（可选，不传则获取当前登录医生）

- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "doctorId": "doc001",
    "name": "李医生",
    "phone": "13900139000",
    "email": "<EMAIL>",
    "specialty": "口腔正畸",
    "hospital": "北京口腔医院",
    "department": "正畸科",
    "title": "主治医师",
    "experience": 5,
    "licenseNumber": "110101199001011234",
    "avatar": "https://example.com/doctor-avatar.jpg",
    "introduction": "专业从事口腔正畸治疗，擅长各种牙齿矫正技术",
    "rating": 4.8,
    "reviewCount": 156,
    "registrationDate": "2023-01-10T08:00:00Z",
    "lastLoginTime": "2023-11-15T09:30:00Z"
  }
}
```

### 1.2 更新医生个人信息
- **URL**: `/doctors/{doctorId}`
- **方法**: `PUT`
- **描述**: 更新医生个人信息
- **请求参数**:
```json
{
  "name": "李医生",
  "email": "<EMAIL>",
  "specialty": "口腔正畸",
  "hospital": "北京口腔医院",
  "department": "正畸科",
  "title": "副主任医师",
  "experience": 6,
  "avatar": "https://example.com/new-avatar.jpg",
  "introduction": "更新后的个人介绍"
}
```

## 2. 医生时间管理

### 2.1 获取医生可预约时间
- **URL**: `/doctors/{doctorId}/available-times`
- **方法**: `GET`
- **描述**: 获取医生的可预约时间段
- **查询参数**:
  - `startDate`: 开始日期（YYYY-MM-DD）
  - `endDate`: 结束日期（YYYY-MM-DD）
  - `status`: 时间段状态（available/booked）

- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "availableTimes": [
      {
        "timeSlotId": "ts001",
        "date": "2023-11-20",
        "startTime": "09:00",
        "endTime": "09:30",
        "status": "available",
        "appointmentType": "consultation",
        "maxPatients": 1,
        "bookedPatients": 0
      },
      {
        "timeSlotId": "ts002",
        "date": "2023-11-20",
        "startTime": "09:30",
        "endTime": "10:00",
        "status": "booked",
        "appointmentType": "consultation",
        "maxPatients": 1,
        "bookedPatients": 1,
        "patientName": "张三"
      }
    ]
  }
}
```

### 2.2 添加可预约时间
- **URL**: `/doctors/{doctorId}/available-times`
- **方法**: `POST`
- **描述**: 医生添加新的可预约时间段
- **请求参数**:
```json
{
  "date": "2023-11-25",
  "timeSlots": [
    {
      "startTime": "09:00",
      "endTime": "09:30",
      "appointmentType": "consultation",
      "maxPatients": 1
    },
    {
      "startTime": "09:30",
      "endTime": "10:00",
      "appointmentType": "consultation",
      "maxPatients": 1
    }
  ]
}
```

### 2.3 删除可预约时间
- **URL**: `/doctors/{doctorId}/available-times/{timeSlotId}`
- **方法**: `DELETE`
- **描述**: 删除指定的时间段
- **路径参数**:
  - `timeSlotId`: 时间段ID

## 3. 医生预约管理

### 3.1 获取医生预约列表
- **URL**: `/doctors/{doctorId}/appointments`
- **方法**: `GET`
- **描述**: 获取医生的预约列表
- **查询参数**:
  - `status`: 预约状态（pending/confirmed/completed/cancelled）
  - `startDate`: 开始日期
  - `endDate`: 结束日期
  - `page`: 页码
  - `limit`: 每页数量

- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "appointments": [
      {
        "appointmentId": "apt001",
        "patientId": "pat001",
        "patientName": "张三",
        "patientPhone": "13800138000",
        "appointmentTime": "2023-11-20T14:00:00Z",
        "status": "confirmed",
        "symptoms": "牙齿不齐，想要矫正",
        "notes": "初诊咨询",
        "appointmentType": "consultation",
        "createdAt": "2023-11-15T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 15,
      "totalPages": 2
    }
  }
}
```

### 3.2 确认预约
- **URL**: `/doctors/{doctorId}/appointments/{appointmentId}/confirm`
- **方法**: `PUT`
- **描述**: 医生确认预约
- **请求参数**:
```json
{
  "notes": "已确认预约，请按时到诊"
}
```

### 3.3 取消预约
- **URL**: `/doctors/{doctorId}/appointments/{appointmentId}/cancel`
- **方法**: `PUT`
- **描述**: 医生取消预约
- **请求参数**:
```json
{
  "reason": "医生临时有急诊手术",
  "notes": "建议改约到明天同一时间"
}
```

## 4. 医生评价管理

### 4.1 获取医生评价列表
- **URL**: `/doctors/{doctorId}/ratings`
- **方法**: `GET`
- **描述**: 获取医生的评价列表
- **查询参数**:
  - `rating`: 评分筛选（1-5）
  - `page`: 页码
  - `limit`: 每页数量

- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "ratings": [
      {
        "ratingId": "rat001",
        "patientName": "张***",
        "rating": 5,
        "comment": "医生很专业，服务态度很好",
        "appointmentDate": "2023-11-10T14:00:00Z",
        "createdAt": "2023-11-11T10:00:00Z"
      }
    ],
    "summary": {
      "averageRating": 4.8,
      "totalRatings": 156,
      "ratingDistribution": {
        "5": 120,
        "4": 25,
        "3": 8,
        "2": 2,
        "1": 1
      }
    },
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 156,
      "totalPages": 16
    }
  }
}
```

## 5. 医生通知管理

### 5.1 获取医生通知列表
- **URL**: `/doctors/{doctorId}/notifications`
- **方法**: `GET`
- **描述**: 获取医生的通知消息
- **查询参数**:
  - `type`: 通知类型（appointment/system/rating）
  - `status`: 状态（unread/read）
  - `page`: 页码
  - `limit`: 每页数量

- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "notifications": [
      {
        "notificationId": "not001",
        "type": "appointment",
        "title": "新预约提醒",
        "content": "患者张三预约了您明天14:00的时间",
        "status": "unread",
        "relatedId": "apt001",
        "createdAt": "2023-11-15T10:00:00Z"
      }
    ],
    "unreadCount": 5,
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    }
  }
}
```

### 5.2 标记通知为已读
- **URL**: `/doctors/{doctorId}/notifications/{notificationId}/read`
- **方法**: `PUT`
- **描述**: 标记指定通知为已读
