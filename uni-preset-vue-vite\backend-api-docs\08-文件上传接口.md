# 文件上传相关接口

## 基础信息
- **基础URL**: `https://api.dentalcare.com/v1`
- **认证方式**: JWT <PERSON> (Bearer Authentication)
- **请求头**: `Authorization: Bearer {token}`

## 1. 文件上传

### 1.1 上传头像
- **URL**: `/upload/avatar`
- **方法**: `POST`
- **描述**: 上传用户头像
- **请求格式**: `multipart/form-data`
- **请求参数**:
  - `file`: 图片文件（必需）
  - `userType`: 用户类型（patient/doctor）

- **文件限制**:
  - 支持格式：jpg, jpeg, png, gif
  - 文件大小：最大 5MB
  - 图片尺寸：建议 200x200 到 800x800 像素

- **成功响应**:
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "fileId": "file_001",
    "fileName": "avatar.jpg",
    "originalName": "user_avatar.jpg",
    "fileSize": 245760,
    "mimeType": "image/jpeg",
    "url": "https://cdn.dentalcare.com/avatars/user_001/avatar.jpg",
    "thumbnailUrl": "https://cdn.dentalcare.com/avatars/user_001/avatar_thumb.jpg",
    "uploadedAt": "2023-11-16T10:30:00Z"
  }
}
```

### 1.2 上传症状图片
- **URL**: `/upload/symptom-images`
- **方法**: `POST`
- **描述**: 上传患者症状图片
- **请求格式**: `multipart/form-data`
- **请求参数**:
  - `files`: 图片文件数组（最多5张）
  - `symptomId`: 症状记录ID（可选）

- **文件限制**:
  - 支持格式：jpg, jpeg, png
  - 单个文件大小：最大 10MB
  - 图片尺寸：建议最小 400x400 像素
  - 数量限制：单次最多上传 5 张

- **成功响应**:
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "uploadedFiles": [
      {
        "fileId": "file_002",
        "fileName": "symptom_1.jpg",
        "originalName": "tooth_pain.jpg",
        "fileSize": 1024000,
        "mimeType": "image/jpeg",
        "url": "https://cdn.dentalcare.com/symptoms/patient_001/symptom_1.jpg",
        "thumbnailUrl": "https://cdn.dentalcare.com/symptoms/patient_001/symptom_1_thumb.jpg",
        "uploadedAt": "2023-11-16T10:30:00Z"
      }
    ],
    "totalUploaded": 1,
    "failedUploads": []
  }
}
```

### 1.3 上传医疗文档
- **URL**: `/upload/medical-documents`
- **方法**: `POST`
- **描述**: 上传医疗相关文档
- **请求格式**: `multipart/form-data`
- **请求参数**:
  - `file`: 文档文件
  - `documentType`: 文档类型（prescription/report/certificate）
  - `appointmentId`: 关联预约ID（可选）
  - `description`: 文档描述

- **文件限制**:
  - 支持格式：pdf, doc, docx, jpg, jpeg, png
  - 文件大小：最大 20MB

- **成功响应**:
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "fileId": "file_003",
    "fileName": "prescription_001.pdf",
    "originalName": "处方单.pdf",
    "fileSize": 2048000,
    "mimeType": "application/pdf",
    "url": "https://cdn.dentalcare.com/documents/doc_001/prescription_001.pdf",
    "documentType": "prescription",
    "description": "口腔正畸治疗处方",
    "uploadedAt": "2023-11-16T10:30:00Z"
  }
}
```

## 2. 文件管理

### 2.1 获取用户文件列表
- **URL**: `/files`
- **方法**: `GET`
- **描述**: 获取当前用户的文件列表
- **查询参数**:
  - `type`: 文件类型（avatar/symptom/document）
  - `page`: 页码
  - `limit`: 每页数量

- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "files": [
      {
        "fileId": "file_001",
        "fileName": "avatar.jpg",
        "originalName": "user_avatar.jpg",
        "fileSize": 245760,
        "mimeType": "image/jpeg",
        "type": "avatar",
        "url": "https://cdn.dentalcare.com/avatars/user_001/avatar.jpg",
        "uploadedAt": "2023-11-16T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 15,
      "totalPages": 2
    }
  }
}
```

### 2.2 获取文件详情
- **URL**: `/files/{fileId}`
- **方法**: `GET`
- **描述**: 获取指定文件的详细信息
- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "fileId": "file_001",
    "fileName": "avatar.jpg",
    "originalName": "user_avatar.jpg",
    "fileSize": 245760,
    "mimeType": "image/jpeg",
    "type": "avatar",
    "url": "https://cdn.dentalcare.com/avatars/user_001/avatar.jpg",
    "thumbnailUrl": "https://cdn.dentalcare.com/avatars/user_001/avatar_thumb.jpg",
    "uploadedAt": "2023-11-16T10:30:00Z",
    "metadata": {
      "width": 400,
      "height": 400,
      "format": "JPEG"
    }
  }
}
```

### 2.3 删除文件
- **URL**: `/files/{fileId}`
- **方法**: `DELETE`
- **描述**: 删除指定文件
- **成功响应**:
```json
{
  "code": 200,
  "message": "删除成功",
  "data": {
    "fileId": "file_001",
    "deletedAt": "2023-11-16T11:00:00Z"
  }
}
```

## 3. 文件下载

### 3.1 生成下载链接
- **URL**: `/files/{fileId}/download-url`
- **方法**: `GET`
- **描述**: 生成文件的临时下载链接
- **查询参数**:
  - `expireMinutes`: 链接有效期（分钟，默认60）

- **成功响应**:
```json
{
  "code": 200,
  "message": "生成成功",
  "data": {
    "downloadUrl": "https://cdn.dentalcare.com/download/temp_token_123456",
    "expireAt": "2023-11-16T12:00:00Z",
    "fileName": "avatar.jpg"
  }
}
```

### 3.2 批量下载
- **URL**: `/files/batch-download`
- **方法**: `POST`
- **描述**: 批量下载文件（生成压缩包）
- **请求参数**:
```json
{
  "fileIds": ["file_001", "file_002", "file_003"],
  "archiveName": "medical_documents.zip"
}
```

- **成功响应**:
```json
{
  "code": 200,
  "message": "打包成功",
  "data": {
    "archiveId": "archive_001",
    "downloadUrl": "https://cdn.dentalcare.com/archives/archive_001.zip",
    "fileCount": 3,
    "totalSize": 5120000,
    "expireAt": "2023-11-17T10:30:00Z"
  }
}
```

## 4. 图片处理

### 4.1 图片裁剪
- **URL**: `/files/{fileId}/crop`
- **方法**: `POST`
- **描述**: 裁剪图片
- **请求参数**:
```json
{
  "x": 50,
  "y": 50,
  "width": 200,
  "height": 200,
  "quality": 90
}
```

### 4.2 生成缩略图
- **URL**: `/files/{fileId}/thumbnail`
- **方法**: `POST`
- **描述**: 生成图片缩略图
- **请求参数**:
```json
{
  "width": 150,
  "height": 150,
  "quality": 80,
  "format": "jpeg"
}
```

## 5. 文件安全

### 5.1 文件病毒扫描
- **URL**: `/files/{fileId}/scan`
- **方法**: `POST`
- **描述**: 对上传的文件进行病毒扫描
- **成功响应**:
```json
{
  "code": 200,
  "message": "扫描完成",
  "data": {
    "fileId": "file_001",
    "scanResult": "clean",
    "scanEngine": "ClamAV",
    "scannedAt": "2023-11-16T10:35:00Z"
  }
}
```

### 5.2 获取文件访问权限
- **URL**: `/files/{fileId}/permissions`
- **方法**: `GET`
- **描述**: 获取文件的访问权限设置

### 5.3 设置文件访问权限
- **URL**: `/files/{fileId}/permissions`
- **方法**: `PUT`
- **描述**: 设置文件的访问权限
- **请求参数**:
```json
{
  "visibility": "private",
  "allowedUsers": ["user_001", "user_002"],
  "allowedRoles": ["doctor"],
  "expireAt": "2023-12-16T10:30:00Z"
}
```

## 错误响应示例

### 文件格式不支持
```json
{
  "code": 400,
  "message": "不支持的文件格式",
  "details": {
    "supportedFormats": ["jpg", "jpeg", "png", "gif"],
    "receivedFormat": "bmp"
  }
}
```

### 文件大小超限
```json
{
  "code": 413,
  "message": "文件大小超出限制",
  "details": {
    "maxSize": 5242880,
    "receivedSize": 10485760,
    "maxSizeFormatted": "5MB"
  }
}
```

### 存储空间不足
```json
{
  "code": 507,
  "message": "存储空间不足",
  "details": {
    "availableSpace": 1048576,
    "requiredSpace": 5242880
  }
}
```
