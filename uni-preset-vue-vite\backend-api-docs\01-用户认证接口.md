# 用户认证相关接口

## 基础信息
- **基础URL**: `https://api.dentalcare.com/v1`
- **数据格式**: JSON
- **认证方式**: JWT <PERSON> (Bearer Authentication)

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": { ... }
}
```

### 错误响应
```json
{
  "code": 400-500,
  "message": "错误描述"
}
```

## 1. 用户注册

### 1.1 患者注册
- **URL**: `/auth/register`
- **方法**: `POST`
- **描述**: 患者用户注册
- **请求参数**:
```json
{
  "name": "张三",
  "phone": "***********",
  "email": "<EMAIL>",
  "password": "password123",
  "confirmPassword": "password123",
  "userType": "patient",
  "gender": "男",
  "age": 28,
  "address": "北京市朝阳区"
}
```

- **成功响应**:
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "userId": "12345",
    "userType": "patient",
    "name": "张三",
    "phone": "***********",
    "email": "<EMAIL>"
  }
}
```

### 1.2 医生注册
- **URL**: `/auth/register`
- **方法**: `POST`
- **描述**: 医生用户注册
- **请求参数**:
```json
{
  "name": "李医生",
  "phone": "***********",
  "email": "<EMAIL>",
  "password": "password123",
  "confirmPassword": "password123",
  "userType": "doctor",
  "specialty": "口腔正畸",
  "hospital": "北京口腔医院",
  "department": "正畸科",
  "title": "主治医师",
  "experience": 5,
  "licenseNumber": "110101199001011234"
}
```

## 2. 用户登录

### 2.1 统一登录接口
- **URL**: `/auth/login`
- **方法**: `POST`
- **描述**: 用户登录（患者/医生/管理员）
- **请求参数**:
```json
{
  "account": "***********",
  "password": "password123",
  "userType": "patient"
}
```

- **成功响应**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "userId": "12345",
    "userType": "patient",
    "name": "张三",
    "phone": "***********",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expireTime": *************
  }
}
```

- **错误响应**:
```json
{
  "code": 401,
  "message": "账号或密码错误"
}
```

## 3. 密码重置

### 3.1 发送验证码
- **URL**: `/auth/send-verification-code`
- **方法**: `POST`
- **描述**: 发送手机验证码
- **请求参数**:
```json
{
  "phone": "***********",
  "type": "reset_password"
}
```

### 3.2 重置密码
- **URL**: `/auth/reset-password`
- **方法**: `POST`
- **描述**: 通过验证码重置密码
- **请求参数**:
```json
{
  "phone": "***********",
  "verificationCode": "123456",
  "newPassword": "newpassword123"
}
```

## 4. 退出登录

### 4.1 退出登录
- **URL**: `/auth/logout`
- **方法**: `POST`
- **描述**: 用户退出登录
- **请求头**: `Authorization: Bearer {token}`
- **成功响应**:
```json
{
  "code": 200,
  "message": "退出成功"
}
```

## 5. Token验证

### 5.1 验证Token
- **URL**: `/auth/verify-token`
- **方法**: `GET`
- **描述**: 验证Token有效性
- **请求头**: `Authorization: Bearer {token}`
- **成功响应**:
```json
{
  "code": 200,
  "message": "Token有效",
  "data": {
    "userId": "12345",
    "userType": "patient",
    "name": "张三"
  }
}
```

## 状态码说明
- 200: 成功
- 400: 请求参数错误
- 401: 未授权（未登录或token过期）
- 403: 权限不足
- 409: 资源冲突（如手机号已注册）
- 500: 服务器内部错误
