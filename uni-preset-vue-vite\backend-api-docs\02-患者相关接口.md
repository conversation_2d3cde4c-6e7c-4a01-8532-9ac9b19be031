# 患者相关接口

## 基础信息
- **基础URL**: `https://api.dentalcare.com/v1`
- **认证方式**: JWT <PERSON> (Bearer Authentication)
- **请求头**: `Authorization: Bearer {token}`

## 1. 患者信息管理

### 1.1 获取患者个人信息
- **URL**: `/patients/{patientId}`
- **方法**: `GET`
- **描述**: 获取患者个人详细信息
- **路径参数**:
  - `patientId`: 患者ID（可选，不传则获取当前登录患者）

- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "userId": "12345",
    "name": "张三",
    "phone": "13800138000",
    "email": "<EMAIL>",
    "gender": "男",
    "age": 28,
    "address": "北京市朝阳区",
    "avatar": "https://example.com/avatar.jpg",
    "registrationDate": "2023-01-15T08:30:00Z",
    "lastLoginTime": "2023-11-15T10:20:00Z"
  }
}
```

### 1.2 更新患者个人信息
- **URL**: `/patients/{patientId}`
- **方法**: `PUT`
- **描述**: 更新患者个人信息
- **请求参数**:
```json
{
  "name": "张三",
  "email": "<EMAIL>",
  "gender": "男",
  "age": 29,
  "address": "北京市海淀区",
  "avatar": "https://example.com/new-avatar.jpg"
}
```

- **成功响应**:
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "userId": "12345",
    "name": "张三",
    "email": "<EMAIL>",
    "updatedAt": "2023-11-15T10:30:00Z"
  }
}
```

## 2. 医生推荐

### 2.1 获取推荐医生列表
- **URL**: `/patients/recommended-doctors`
- **方法**: `GET`
- **描述**: 基于位置和专科获取推荐医生
- **查询参数**:
  - `specialty`: 专科类型（可选）
  - `location`: 位置信息（可选）
  - `page`: 页码（默认1）
  - `limit`: 每页数量（默认10）

- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "doctors": [
      {
        "doctorId": "doc001",
        "name": "李医生",
        "specialty": "口腔正畸",
        "hospital": "北京口腔医院",
        "department": "正畸科",
        "title": "主治医师",
        "experience": 5,
        "rating": 4.8,
        "reviewCount": 156,
        "avatar": "https://example.com/doctor-avatar.jpg",
        "introduction": "专业从事口腔正畸治疗...",
        "availableToday": true,
        "nextAvailableTime": "2023-11-16T09:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    }
  }
}
```

## 3. 患者预约管理

### 3.1 获取患者预约列表
- **URL**: `/patients/{patientId}/appointments`
- **方法**: `GET`
- **描述**: 获取患者的预约记录
- **查询参数**:
  - `status`: 预约状态（pending/confirmed/completed/cancelled）
  - `startDate`: 开始日期
  - `endDate`: 结束日期
  - `page`: 页码
  - `limit`: 每页数量

- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "appointments": [
      {
        "appointmentId": "apt001",
        "doctorId": "doc001",
        "doctorName": "李医生",
        "specialty": "口腔正畸",
        "hospital": "北京口腔医院",
        "appointmentTime": "2023-11-20T14:00:00Z",
        "status": "confirmed",
        "symptoms": "牙齿不齐，想要矫正",
        "notes": "初诊咨询",
        "createdAt": "2023-11-15T10:00:00Z",
        "updatedAt": "2023-11-15T11:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 5,
      "totalPages": 1
    }
  }
}
```

## 4. 健康数据管理

### 4.1 获取患者健康数据
- **URL**: `/patients/{patientId}/health-data`
- **方法**: `GET`
- **描述**: 获取患者口腔健康数据
- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "healthScore": 85,
    "lastCheckDate": "2023-10-15T09:00:00Z",
    "cavityRate": "5.20%",
    "periodontalRate": "2.30%",
    "plaqueRate": "8.50%",
    "cleanlinessScore": 88,
    "riskLevel": "low",
    "recommendations": [
      "建议每天使用牙线清洁牙缝",
      "定期进行口腔检查"
    ]
  }
}
```

### 4.2 更新患者健康数据
- **URL**: `/patients/{patientId}/health-data`
- **方法**: `PUT`
- **描述**: 更新患者口腔健康数据
- **请求参数**:
```json
{
  "healthScore": 85,
  "cavityRate": "5.20%",
  "periodontalRate": "2.30%",
  "plaqueRate": "8.50%",
  "cleanlinessScore": 88,
  "checkDate": "2023-10-15T09:00:00Z",
  "notes": "口腔状况良好，建议继续保持"
}
```

## 5. 患者症状描述

### 5.1 提交症状描述
- **URL**: `/patients/{patientId}/symptoms`
- **方法**: `POST`
- **描述**: 患者提交症状描述
- **请求参数**:
```json
{
  "description": "最近牙龈经常出血，刷牙时特别明显",
  "severity": "moderate",
  "duration": "2周",
  "images": [
    "https://example.com/symptom1.jpg",
    "https://example.com/symptom2.jpg"
  ]
}
```

### 5.2 获取症状历史
- **URL**: `/patients/{patientId}/symptoms`
- **方法**: `GET`
- **描述**: 获取患者症状描述历史
- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "symptoms": [
      {
        "symptomId": "sym001",
        "description": "最近牙龈经常出血，刷牙时特别明显",
        "severity": "moderate",
        "duration": "2周",
        "images": ["https://example.com/symptom1.jpg"],
        "createdAt": "2023-11-14T09:15:00Z"
      }
    ]
  }
}
```
