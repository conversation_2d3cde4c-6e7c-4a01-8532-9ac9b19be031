<template>
  <view class="container">
    <!-- 顶部标题 -->
    <view class="header">
      <view class="back-button" @tap="goBack">
        <text class="back-icon">←</text>
      </view>
      <text class="title">{{ isEdit ? '编辑用户信息' : '添加用户' }}</text>
    </view>
    
    <!-- 表单内容 -->
    <view class="form-content">
      <!-- 姓名 -->
      <view class="form-item">
        <text class="form-label">姓名</text>
        <input class="form-input" v-model="patientInfo.name" placeholder="请输入用户姓名" />
      </view>
      
      <!-- 性别 -->
      <view class="form-item">
        <text class="form-label">性别</text>
        <view class="gender-options">
          <view 
            class="gender-option" 
            :class="{ active: patientInfo.gender === '男' }"
            @tap="patientInfo.gender = '男'"
          >
            <text>男</text>
          </view>
          <view 
            class="gender-option" 
            :class="{ active: patientInfo.gender === '女' }"
            @tap="patientInfo.gender = '女'"
          >
            <text>女</text>
          </view>
        </view>
      </view>
      
      <!-- 手机号 -->
      <view class="form-item">
        <text class="form-label">手机号</text>
        <input class="form-input" v-model="patientInfo.phone" type="number" placeholder="请输入手机号" maxlength="11" />
      </view>
      
      <!-- 年龄 -->
      <view class="form-item">
        <text class="form-label">年龄</text>
        <input class="form-input" v-model="patientInfo.age" type="number" placeholder="请输入年龄" />
      </view>
      
      <!-- 地址 -->
      <view class="form-item">
        <text class="form-label">地址</text>
        <textarea class="form-textarea" v-model="patientInfo.address" placeholder="请输入详细地址" />
      </view>
    </view>
    
    <!-- 保存按钮 -->
    <view class="button-container">
      <button class="save-button" @tap="savePatientInfo">保存</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 是否为编辑模式
const isEdit = ref(false);

// 用户信息
const patientInfo = ref({
  id: '',
  name: '',
  gender: '男',
  phone: '',
  age: '',
  address: ''
});

// 页面加载
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.$page?.options || {};
  
  // 判断是否为编辑模式
  if (options.mode === 'edit' && options.id) {
    isEdit.value = true;
    
    // 如果是编辑模式，获取用户信息
    try {
      const editInfo = uni.getStorageSync('editPatientInfo');
      if (editInfo && editInfo.id === options.id) {
        patientInfo.value = { ...editInfo };
      } else {
        // 如果没有缓存数据，可以从API获取
        // 这里使用模拟数据
        fetchPatientInfo(options.id);
      }
    } catch (e) {
      console.error('获取用户信息失败', e);
      uni.showToast({
        title: '获取信息失败',
        icon: 'none'
      });
    }
  } else {
    // 如果是添加模式，生成一个新的用户ID
    patientInfo.value.id = generatePatientId();
  }
});

// 获取用户信息
const fetchPatientInfo = (id) => {
  // 实际项目中应该从API获取数据
  // 这里使用模拟数据
  patientInfo.value = {
    id: id,
    name: '张三',
    gender: '男',
    phone: '13800138000',
    age: '28',
    address: '北京市朝阳区健康路1号'
  };
};

// 生成用户ID
const generatePatientId = () => {
  // 实际项目中应该由后端生成
  return 'P' + Date.now().toString().substring(6);
};

// 保存用户信息
const savePatientInfo = () => {
  // 表单验证
  if (!patientInfo.value.name) {
    uni.showToast({
      title: '请输入用户姓名',
      icon: 'none'
    });
    return;
  }
  
  if (!patientInfo.value.phone || patientInfo.value.phone.length !== 11) {
    uni.showToast({
      title: '请输入正确的手机号',
      icon: 'none'
    });
    return;
  }
  
  // 保存用户信息
  try {
    // 实际项目中应该调用API保存数据
    // 这里仅作演示
    uni.showLoading({
      title: '保存中...'
    });
    
    setTimeout(() => {
      uni.hideLoading();
      
      uni.showToast({
        title: isEdit.value ? '更新成功' : '添加成功',
        icon: 'success',
        duration: 2000,
        success: () => {
          setTimeout(() => {
            goBack();
          }, 2000);
        }
      });
    }, 1000);
  } catch (e) {
    console.error('保存用户信息失败', e);
    uni.showToast({
      title: '保存失败，请重试',
      icon: 'none'
    });
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background-color: #a8c0ff;
  background-image: linear-gradient(135deg, #a8c0ff 0%, #8e99f3 100%);
  padding: 40rpx 30rpx;
  color: #fff;
  display: flex;
  align-items: center;
  position: relative;
}

.back-button {
  position: absolute;
  left: 30rpx;
  top: 50%;
  transform: translateY(-50%);
}

.back-icon {
  font-size: 40rpx;
  color: #fff;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  flex: 1;
  text-align: center;
}

.form-content {
  flex: 1;
  padding: 30rpx;
}

.form-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.form-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}

.form-input {
  font-size: 32rpx;
  color: #333;
  width: 100%;
  height: 80rpx;
  border-bottom: 1px solid #eee;
}

.form-textarea {
  font-size: 32rpx;
  color: #333;
  width: 100%;
  height: 200rpx;
  border: 1px solid #eee;
  border-radius: 8rpx;
  padding: 20rpx;
  box-sizing: border-box;
}

.gender-options {
  display: flex;
  gap: 30rpx;
}

.gender-option {
  flex: 1;
  height: 80rpx;
  border: 1px solid #ddd;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  color: #666;
  
  &.active {
    background-color: #8e99f3;
    color: #fff;
    border-color: #8e99f3;
  }
}

.button-container {
  padding: 30rpx;
}

.save-button {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background: linear-gradient(135deg, #8e99f3 0%, #4a56e2 100%);
  color: #fff;
  border-radius: 45rpx;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(74, 86, 226, 0.3);
}
</style> 