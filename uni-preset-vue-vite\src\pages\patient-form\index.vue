<template>
  <view class="container">
    <!-- 表单内容 -->
    <view class="form-content">
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-title">基本信息</view>

        <!-- 姓名 -->
        <view class="form-item">
          <text class="form-label">姓名</text>
          <input class="form-input" v-model="patientInfo.name" placeholder="请输入用户姓名" :disabled="isViewMode" />
        </view>

        <!-- 性别 -->
        <view class="form-item">
          <text class="form-label">性别</text>
          <view v-if="!isViewMode" class="gender-options">
            <view
              class="gender-option"
              :class="{ active: patientInfo.gender === '男' }"
              @tap="patientInfo.gender = '男'"
            >
              <text>男</text>
            </view>
            <view
              class="gender-option"
              :class="{ active: patientInfo.gender === '女' }"
              @tap="patientInfo.gender = '女'"
            >
              <text>女</text>
            </view>
          </view>
          <view v-else class="form-input disabled">{{ patientInfo.gender || '未设置' }}</view>
        </view>

        <!-- 手机号 -->
        <view class="form-item">
          <text class="form-label">手机号</text>
          <input class="form-input" v-model="patientInfo.phone" type="number" placeholder="请输入手机号" maxlength="11" :disabled="isViewMode" />
        </view>

        <!-- 邮箱 -->
        <view class="form-item">
          <text class="form-label">邮箱</text>
          <input class="form-input" v-model="patientInfo.email" placeholder="请输入邮箱" :disabled="isViewMode" />
        </view>

        <!-- 密码 - 仅在非查看模式下显示 -->
        <view class="form-item" v-if="!isViewMode">
          <text class="form-label">密码</text>
          <view class="password-input-container">
            <input
              class="form-input"
              v-model="patientInfo.password"
              placeholder="请输入密码"
              :type="showPassword ? 'text' : 'password'"
            />
            <text class="password-toggle" @tap="togglePasswordVisibility">
              {{ showPassword ? '隐藏' : '显示' }}
            </text>
          </view>
        </view>

        <!-- 安全问题 -->
        <view class="form-item">
          <text class="form-label">安全问题</text>
          <input class="form-input" v-model="patientInfo.securityQuestion" placeholder="请输入安全问题" :disabled="isViewMode" />
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 - 仅在非查看模式下显示 -->
    <view class="button-container" v-if="!isViewMode">
      <button class="save-button" @tap="savePatientInfo">保存</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';

// 页面参数
const query = ref({
  mode: 'add',
  id: ''
});

// 是否为编辑模式
const isEdit = computed(() => query.value.mode === 'edit');

// 是否为查看模式
const isViewMode = computed(() => query.value.mode === 'view');

// 是否显示密码
const showPassword = ref(false);

// 用户信息
const patientInfo = ref({
  id: '',
  name: '',
  gender: '男',
  phone: '',
  email: '',
  password: '',
  securityQuestion: ''
});

// 页面加载
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  if (currentPage && currentPage.$page && currentPage.$page.fullPath) {
    const path = currentPage.$page.fullPath;
    const queryParams = path.split('?')[1];
    if (queryParams) {
      queryParams.split('&').forEach(param => {
        const [key, value] = param.split('=');
        if (key && value) {
          query.value[key] = decodeURIComponent(value);
        }
      });
    }
  }

  // 如果是编辑或查看模式，获取用户信息
  if ((isEdit.value || isViewMode.value) && query.value.id) {
    try {
      const storageKey = isViewMode.value ? 'currentViewPatient' : 'editPatientInfo';
      const patientData = uni.getStorageSync(storageKey);
      if (patientData) {
        patientInfo.value = { ...patientInfo.value, ...patientData };
      } else {
        // 如果没有缓存数据，使用模拟数据
        fetchPatientInfo(query.value.id);
      }
    } catch (e) {
      console.error('获取用户信息失败', e);
      uni.showToast({
        title: '获取信息失败',
        icon: 'none'
      });
    }
  } else {
    // 如果是添加模式，生成一个新的用户ID
    patientInfo.value.id = generatePatientId();
  }
});

// 获取用户信息
const fetchPatientInfo = (id) => {
  // 实际项目中应该从API获取数据
  // 这里使用模拟数据
  patientInfo.value = {
    id: id,
    name: '张三',
    gender: '男',
    phone: '13800138000',
    email: '<EMAIL>',
    password: '123456',
    securityQuestion: '您的出生地是哪里？'
  };
};

// 生成用户ID
const generatePatientId = () => {
  // 实际项目中应该由后端生成
  return 'P' + Date.now().toString().substring(6);
};

// 切换密码可见性
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
};

// 保存用户信息
const savePatientInfo = () => {
  // 表单验证
  if (!patientInfo.value.name) {
    uni.showToast({
      title: '请输入用户姓名',
      icon: 'none'
    });
    return;
  }
  
  if (!patientInfo.value.phone || patientInfo.value.phone.length !== 11) {
    uni.showToast({
      title: '请输入正确的手机号',
      icon: 'none'
    });
    return;
  }

  if (!patientInfo.value.email) {
    uni.showToast({
      title: '请输入邮箱',
      icon: 'none'
    });
    return;
  }

  if (!patientInfo.value.password) {
    uni.showToast({
      title: '请输入密码',
      icon: 'none'
    });
    return;
  }
  
  // 保存用户信息
  try {
    // 实际项目中应该调用API保存数据
    // 这里仅作演示
    uni.showLoading({
      title: '保存中...'
    });
    
    setTimeout(() => {
      uni.hideLoading();
      
      uni.showToast({
        title: isEdit.value ? '更新成功' : '添加成功',
        icon: 'success',
        duration: 2000,
        success: () => {
          setTimeout(() => {
            goBack();
          }, 2000);
        }
      });
    }, 1000);
  } catch (e) {
    console.error('保存用户信息失败', e);
    uni.showToast({
      title: '保存失败，请重试',
      icon: 'none'
    });
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}



.form-content {
  flex: 1;
  padding: 30rpx;
}

.form-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.form-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.form-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}

.form-input {
  font-size: 32rpx;
  color: #333;
  width: 100%;
  height: 80rpx;
  border-bottom: 1px solid #eee;
}

.form-input:disabled,
.form-input.disabled {
  background-color: #f0f0f0;
  color: #999;
  cursor: not-allowed;
}

.password-input-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 26rpx;
  color: #8e99f3;
}

.form-textarea {
  font-size: 32rpx;
  color: #333;
  width: 100%;
  height: 200rpx;
  border: 1px solid #eee;
  border-radius: 8rpx;
  padding: 20rpx;
  box-sizing: border-box;
}

.gender-options {
  display: flex;
  gap: 30rpx;
}

.gender-option {
  flex: 1;
  height: 80rpx;
  border: 1px solid #ddd;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  color: #666;
  
  &.active {
    background-color: #8e99f3;
    color: #fff;
    border-color: #8e99f3;
  }
}

.button-container {
  padding: 30rpx;
}

.save-button {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background: linear-gradient(135deg, #8e99f3 0%, #4a56e2 100%);
  color: #fff;
  border-radius: 45rpx;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(74, 86, 226, 0.3);
}
</style> 