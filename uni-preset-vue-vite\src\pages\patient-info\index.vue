<template>
  <view class="container">
    <!-- 基本信息卡片 -->
    <view class="section">
      <view class="section-title">基本信息</view>
      <view class="info-card">
        <view class="info-item">
          <text class="info-label">姓名</text>
          <text class="info-value">{{ patientInfo.name || '未设置' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">电话号码</text>
          <text class="info-value">{{ patientInfo.phone || '未设置' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">性别</text>
          <text class="info-value">{{ patientInfo.gender || '未设置' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">年龄</text>
          <text class="info-value">{{ patientInfo.age || '未设置' }}</text>
        </view>
      </view>
    </view>
    
    <!-- 口腔健康状况卡片 -->
    <view class="section">
      <view class="section-title">口腔健康状况</view>
      <view class="health-card">
        <view class="health-header">
          <view class="health-score-container">
            <view class="health-score">
              <text class="score-number">{{ patientInfo.healthScore || 88 }}</text>
              <text class="score-label">健康分</text>
            </view>
          </view>
          <view class="health-details">
            <text class="check-date">检查日期: {{ patientInfo.checkDate || '2023-10-15' }}</text>
            <view class="health-metrics">
              <view class="metric-item">
                <text class="metric-label">蛀齿比例</text>
                <text class="metric-value">{{ patientInfo.cavityRate || '5.50%' }}</text>
              </view>
              <view class="metric-item">
                <text class="metric-label">牙周比例</text>
                <text class="metric-value">{{ patientInfo.periodontalRate || '3.20%' }}</text>
              </view>
              <view class="metric-item">
                <text class="metric-label">牙菌斑比例</text>
                <text class="metric-value">{{ patientInfo.plaqueRate || '12.30%' }}</text>
              </view>
              <view class="metric-item">
                <text class="metric-label">清洁度评分</text>
                <text class="metric-value">{{ patientInfo.cleanlinessScore || '85' }}</text>
              </view>
            </view>
          </view>
        </view>
        <view class="health-footer">
          <text class="detail-link" @tap="viewHealthDetails">查看详情</text>
        </view>
      </view>
    </view>
    
    <!-- 情况描述卡片 -->
    <view class="section">
      <view class="section-title">情况描述</view>
      <view class="description-card">
        <view class="description-content">
          <text class="description-text">{{ patientInfo.description || '无' }}</text>
        </view>
        <view class="description-meta" v-if="patientInfo.description">
          <text class="description-date">描述时间: {{ patientInfo.descriptionDate || '2023-11-15 14:30' }}</text>
        </view>
      </view>
    </view>
    
    <!-- 健康趋势卡片 -->
    <view class="section">
      <view class="section-title">健康趋势</view>
      <view class="trend-card">
        <view class="trend-header">
          <text class="trend-title">口腔健康评分趋势</text>
        </view>
        <view class="trend-chart">
          <!-- 这里可以集成图表组件，目前用简单的模拟 -->
          <view class="chart-placeholder">
            <text class="chart-text">健康评分趋势图</text>
            <text class="chart-desc">最近3个月评分变化</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 患者信息
const patientInfo = ref({
  name: '',
  phone: '',
  gender: '',
  age: '',
  healthScore: 88,
  checkDate: '2023-10-15',
  cavityRate: '5.50%',
  periodontalRate: '3.20%',
  plaqueRate: '12.30%',
  cleanlinessScore: '85',
  description: '',
  descriptionDate: ''
});

// 页面加载时
onMounted(() => {
  // 获取传递的患者信息
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options;
  
  if (options.phone) {
    // 根据电话号码获取患者信息
    fetchPatientInfo(options.phone);
  }
});

// 获取患者信息
const fetchPatientInfo = (phone) => {
  // 模拟根据电话号码获取患者详细信息
  const mockPatientData = {
    '13812345678': {
      name: '张三',
      phone: '13812345678',
      gender: '男',
      age: '28',
      healthScore: 88,
      checkDate: '2023-10-15',
      cavityRate: '5.50%',
      periodontalRate: '3.20%',
      plaqueRate: '12.30%',
      cleanlinessScore: '85',
      description: '最近牙齿有些疼痛，特别是吃冷热食物的时候，希望医生能帮忙检查一下是否有蛀牙或其他问题。',
      descriptionDate: '2023-11-15 14:30'
    },
    '13987654321': {
      name: '李四',
      phone: '13987654321',
      gender: '女',
      age: '32',
      healthScore: 92,
      checkDate: '2023-10-20',
      cavityRate: '2.30%',
      periodontalRate: '1.80%',
      plaqueRate: '8.50%',
      cleanlinessScore: '92',
      description: '', // 直接预约，无描述
      descriptionDate: ''
    },
    '13700001111': {
      name: '王五',
      phone: '13700001111',
      gender: '男',
      age: '45',
      healthScore: 75,
      checkDate: '2023-10-12',
      cavityRate: '8.20%',
      periodontalRate: '5.60%',
      plaqueRate: '15.80%',
      cleanlinessScore: '78',
      description: '牙龈经常出血，刷牙时特别明显，担心是牙周病，想做个全面检查。',
      descriptionDate: '2023-11-14 09:15'
    }
  };
  
  const data = mockPatientData[phone];
  if (data) {
    patientInfo.value = data;
  } else {
    // 如果没有找到对应数据，使用默认数据
    patientInfo.value.phone = phone;
    patientInfo.value.name = '未知患者';
  }
};

// 查看健康详情
const viewHealthDetails = () => {
  uni.showToast({
    title: '查看健康详情',
    icon: 'none'
  });
};
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #f8f8f8;
}

/* 区域样式 */
.section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #4a90e2;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  border-left: 8rpx solid #4a90e2;
  display: flex;
  align-items: center;
}

/* 基本信息卡片样式 */
.info-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(168, 192, 255, 0.2);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

/* 健康状况卡片样式 */
.health-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(168, 192, 255, 0.2);
}

.health-header {
  display: flex;
  margin-bottom: 30rpx;
}

.health-score-container {
  margin-right: 40rpx;
}

.health-score {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #fff;
}

.score-number {
  font-size: 36rpx;
  font-weight: bold;
  line-height: 1;
}

.score-label {
  font-size: 20rpx;
  margin-top: 4rpx;
}

.health-details {
  flex: 1;
}

.check-date {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;
}

.health-metrics {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  font-size: 26rpx;
  color: #666;
}

.metric-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 600;
}

.health-footer {
  text-align: right;
}

.detail-link {
  font-size: 26rpx;
  color: #4a90e2;
  text-decoration: underline;
}

/* 情况描述卡片样式 */
.description-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(168, 192, 255, 0.2);
}

.description-content {
  margin-bottom: 20rpx;
}

.description-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  display: block;
}

.description-meta {
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.description-date {
  font-size: 24rpx;
  color: #999;
}

/* 健康趋势卡片样式 */
.trend-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(168, 192, 255, 0.2);
}

.trend-header {
  margin-bottom: 30rpx;
}

.trend-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.trend-chart {
  height: 300rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart-placeholder {
  text-align: center;
}

.chart-text {
  font-size: 32rpx;
  color: #4a90e2;
  font-weight: 600;
  display: block;
  margin-bottom: 10rpx;
}

.chart-desc {
  font-size: 24rpx;
  color: #999;
}
</style>
