# 口腔健康预约系统 API 文档

## 简介

本文档提供口腔健康预约系统的后端API接口规范，供前端开发人员参考。系统主要包含三种用户角色：患者、医生和管理员，每种角色有不同的功能权限和接口需求。

本API文档集包含以下内容：
- [API接口规范](./README.md)：详细的API接口说明
- [数据库设计](./数据库设计.md)：系统数据库表结构设计
- [开发指南](./开发指南.md)：前后端交互流程和开发规范

## 目录
1. [用户认证相关接口](#用户认证相关接口)
2. [患者相关接口](#患者相关接口)
3. [医生相关接口](#医生相关接口)
4. [管理员相关接口](#管理员相关接口)
5. [预约相关接口](#预约相关接口)
6. [评价相关接口](#评价相关接口)
7. [通知相关接口](#通知相关接口)

## 接口规范

### 基础URL
```
https://api.dentalcare.com/v1
```

### 响应格式
所有API响应均为JSON格式，基本结构如下：

```json
{
  "code": 200,        // 状态码：200成功，非200表示错误
  "message": "成功",   // 状态描述
  "data": {}          // 响应数据，错误时可能为null或错误详情
}
```

### 认证方式
除登录注册等少数接口外，其他接口均需要在请求头中携带token进行认证：

```
Authorization: Bearer {token}
```

## 用户认证相关接口

### 1. 用户注册

**接口**：`POST /auth/register`

**请求参数**：
```json
{
  "userType": "patient",  // 用户类型：patient(患者)、doctor(医生)
  "phone": "***********", // 手机号码，手机号和邮箱至少填一个
  "email": "<EMAIL>", // 邮箱，手机号和邮箱至少填一个
  "password": "password123", // 密码
  "securityQuestion": "我的出生地是？", // 安全问题
  "securityAnswer": "北京", // 安全问题答案
  "name": "张三", // 用户姓名
  "gender": "male", // 性别：male(男)、female(女)
  "avatar": "base64编码的图片" // 可选，头像
}
```

**响应**：
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "userId": "12345",
    "userType": "patient",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### 2. 用户登录

**接口**：`POST /auth/login`

**请求参数**：
```json
{
  "account": "***********", // 手机号或邮箱
  "password": "password123", // 密码
  "userType": "patient" // 用户类型：patient(患者)、doctor(医生)、admin(管理员)
}
```

**响应**：
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "userId": "12345",
    "userType": "patient",
    "name": "张三",
    "avatar": "头像URL",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### 3. 忘记密码

**接口**：`POST /auth/forgot-password`

**请求参数**：
```json
{
  "account": "***********", // 手机号或邮箱
  "securityQuestion": "我的出生地是？", // 安全问题
  "securityAnswer": "北京", // 安全问题答案
  "newPassword": "newpassword123" // 新密码
}
```

**响应**：
```json
{
  "code": 200,
  "message": "密码重置成功",
  "data": null
}
```

### 4. 修改密码

**接口**：`PUT /auth/change-password`

**请求参数**：
```json
{
  "oldPassword": "password123", // 旧密码
  "newPassword": "newpassword123" // 新密码
}
```

**响应**：
```json
{
  "code": 200,
  "message": "密码修改成功",
  "data": null
}
```

### 5. 获取用户信息

**接口**：`GET /auth/user-info`

**响应**：
```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "userId": "12345",
    "userType": "patient",
    "name": "张三",
    "gender": "male",
    "phone": "***********",
    "email": "<EMAIL>",
    "avatar": "头像URL",
    "createdAt": "2023-01-01T00:00:00Z"
  }
}
```

### 6. 更新用户信息

**接口**：`PUT /auth/user-info`

**请求参数**：
```json
{
  "name": "张三", // 可选
  "gender": "male", // 可选
  "phone": "***********", // 可选
  "email": "<EMAIL>", // 可选
  "avatar": "base64编码的图片" // 可选
}
```

**响应**：
```json
{
  "code": 200,
  "message": "用户信息更新成功",
  "data": {
    "userId": "12345",
    "name": "张三",
    "gender": "male",
    "phone": "***********",
    "email": "<EMAIL>",
    "avatar": "头像URL"
  }
}
```

### 7. 退出登录

**接口**：`POST /auth/logout`

**响应**：
```json
{
  "code": 200,
  "message": "退出成功",
  "data": null
}
```

## 患者相关接口

### 1. 获取患者位置

**接口**：`POST /patient/location`

**请求参数**：
```json
{
  "latitude": 39.9042, // 纬度
  "longitude": 116.4074, // 经度
  "address": "北京市海淀区" // 可选，地址描述
}
```

**响应**：
```json
{
  "code": 200,
  "message": "位置更新成功",
  "data": null
}
```

### 2. 获取推荐医生列表

**接口**：`GET /patient/recommended-doctors`

**请求参数**：
```
specialty: 牙齿矫正 // 可选，专科筛选
maxDistance: 10 // 可选，最大距离(公里)
page: 1 // 可选，页码，默认1
pageSize: 10 // 可选，每页数量，默认10
```

**响应**：
```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "total": 50,
    "list": [
      {
        "doctorId": "d10001",
        "name": "李医生",
        "avatar": "头像URL",
        "specialty": "牙齿矫正",
        "hospital": "阳光口腔诊所",
        "address": "北京市海淀区中关村大街1号",
        "distance": 2.5, // 单位：公里
        "rating": 4.8, // 评分
        "ratingCount": 125, // 评价数量
        "introduction": "专注牙齿矫正20年...",
        "latitude": 39.9042,
        "longitude": 116.4074
      }
      // ...更多医生
    ]
  }
}
```

### 3. 获取患者预约列表

**接口**：`GET /patient/appointments`

**请求参数**：
```
status: upcoming // 可选，筛选状态：upcoming(即将到来)、past(历史预约)、cancelled(已取消)
page: 1 // 可选，页码，默认1
pageSize: 10 // 可选，每页数量，默认10
```

**响应**：
```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "total": 5,
    "list": [
      {
        "appointmentId": "a10001",
        "doctorId": "d10001",
        "doctorName": "李医生",
        "doctorAvatar": "头像URL",
        "specialty": "牙齿矫正",
        "hospital": "阳光口腔诊所",
        "address": "北京市海淀区中关村大街1号",
        "appointmentTime": "2023-06-15T14:30:00Z",
        "duration": 30, // 单位：分钟
        "status": "confirmed", // 状态：confirmed(已确认)、completed(已完成)、cancelled(已取消)
        "note": "初次检查" // 预约备注
      }
      // ...更多预约
    ]
  }
}
```

## 医生相关接口

### 1. 获取医生详情

**接口**：`GET /doctor/{doctorId}`

**响应**：
```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "doctorId": "d10001",
    "name": "李医生",
    "avatar": "头像URL",
    "gender": "male",
    "phone": "13900139000",
    "email": "<EMAIL>",
    "specialty": "牙齿矫正",
    "hospital": "阳光口腔诊所",
    "address": "北京市海淀区中关村大街1号",
    "latitude": 39.9042,
    "longitude": 116.4074,
    "introduction": "专注牙齿矫正20年...",
    "rating": 4.8,
    "ratingCount": 125,
    "workingYears": 15 // 工作年限
  }
}
```

### 2. 更新医生专业信息

**接口**：`PUT /doctor/professional-info`

**请求参数**：
```json
{
  "specialty": "牙齿矫正", // 专科
  "hospital": "阳光口腔诊所", // 诊所名称
  "address": "北京市海淀区中关村大街1号", // 诊所地址
  "latitude": 39.9042, // 诊所纬度
  "longitude": 116.4074, // 诊所经度
  "introduction": "专注牙齿矫正20年...", // 个人简介
  "workingYears": 15 // 工作年限
}
```

**响应**：
```json
{
  "code": 200,
  "message": "专业信息更新成功",
  "data": null
}
```

### 3. 获取医生空闲时间段

**接口**：`GET /doctor/available-slots`

**请求参数**：
```
doctorId: d10001 // 医生ID，如果是医生自己查询则不需要
startDate: 2023-06-15 // 开始日期
endDate: 2023-06-30 // 结束日期
```

**响应**：
```json
{
  "code": 200,
  "message": "成功",
  "data": [
    {
      "date": "2023-06-15",
      "slots": [
        {
          "slotId": "s10001",
          "startTime": "09:00",
          "endTime": "09:30",
          "isBooked": false
        },
        {
          "slotId": "s10002",
          "startTime": "09:30",
          "endTime": "10:00",
          "isBooked": true
        }
        // ...更多时间段
      ]
    },
    // ...更多日期
  ]
}
```

### 4. 添加医生空闲时间段

**接口**：`POST /doctor/available-slots`

**请求参数**：
```json
{
  "date": "2023-06-15", // 日期
  "slots": [
    {
      "startTime": "09:00", // 开始时间
      "endTime": "09:30" // 结束时间
    },
    {
      "startTime": "09:30",
      "endTime": "10:00"
    }
    // ...更多时间段
  ]
}
```

**响应**：
```json
{
  "code": 200,
  "message": "空闲时间添加成功",
  "data": null
}
```

### 5. 删除医生空闲时间段

**接口**：`DELETE /doctor/available-slots/{slotId}`

**响应**：
```json
{
  "code": 200,
  "message": "空闲时间删除成功",
  "data": null
}
```

### 6. 批量删除医生空闲时间段

**接口**：`DELETE /doctor/available-slots`

**请求参数**：
```json
{
  "slotIds": ["s10001", "s10002"]
}
```

**响应**：
```json
{
  "code": 200,
  "message": "空闲时间批量删除成功",
  "data": null
}
```

### 7. 获取医生预约列表

**接口**：`GET /doctor/appointments`

**请求参数**：
```
status: upcoming // 可选，筛选状态：upcoming(即将到来)、past(历史预约)、cancelled(已取消)
date: 2023-06-15 // 可选，按日期筛选
page: 1 // 可选，页码，默认1
pageSize: 10 // 可选，每页数量，默认10
```

**响应**：
```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "total": 8,
    "list": [
      {
        "appointmentId": "a10001",
        "patientId": "p10001",
        "patientName": "张三",
        "patientAvatar": "头像URL",
        "patientGender": "male",
        "patientPhone": "***********",
        "appointmentTime": "2023-06-15T14:30:00Z",
        "duration": 30, // 单位：分钟
        "status": "confirmed", // 状态：confirmed(已确认)、completed(已完成)、cancelled(已取消)
        "note": "初次检查" // 预约备注
      }
      // ...更多预约
    ]
  }
}
```

## 管理员相关接口

### 1. 获取用户列表

**接口**：`GET /admin/users`

**请求参数**：
```
userType: patient // 用户类型：patient(患者)、doctor(医生)
keyword: 张三 // 可选，搜索关键词，可搜索姓名、手机号、邮箱
page: 1 // 可选，页码，默认1
pageSize: 10 // 可选，每页数量，默认10
```

**响应**：
```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "total": 100,
    "list": [
      {
        "userId": "p10001",
        "userType": "patient",
        "name": "张三",
        "gender": "male",
        "phone": "***********",
        "email": "<EMAIL>",
        "avatar": "头像URL",
        "createdAt": "2023-01-01T00:00:00Z"
      }
      // ...更多用户
    ]
  }
}
```

### 2. 获取用户详情（包含密码和安全问题）

**接口**：`GET /admin/users/{userId}/detail`

**响应**：
```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "userId": "p10001",
    "userType": "patient",
    "name": "张三",
    "gender": "male",
    "phone": "***********",
    "email": "<EMAIL>",
    "password": "加密后的密码", // 管理员可查看
    "securityQuestion": "我的出生地是？",
    "securityAnswer": "北京",
    "avatar": "头像URL",
    "createdAt": "2023-01-01T00:00:00Z"
  }
}
```

### 3. 修改用户信息（管理员权限）

**接口**：`PUT /admin/users/{userId}`

**请求参数**：
```json
{
  "name": "张三", // 可选
  "gender": "male", // 可选
  "phone": "***********", // 可选
  "email": "<EMAIL>", // 可选
  "password": "newpassword123", // 可选
  "securityQuestion": "我的出生地是？", // 可选
  "securityAnswer": "北京" // 可选
}
```

**响应**：
```json
{
  "code": 200,
  "message": "用户信息修改成功",
  "data": null
}
```

### 4. 删除用户

**接口**：`DELETE /admin/users/{userId}`

**响应**：
```json
{
  "code": 200,
  "message": "用户删除成功",
  "data": null
}
```

### 5. 获取系统统计数据

**接口**：`GET /admin/statistics`

**响应**：
```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "patientCount": 1000, // 患者总数
    "doctorCount": 100, // 医生总数
    "appointmentCount": 5000, // 预约总数
    "todayAppointmentCount": 50, // 今日预约数
    "completedAppointmentCount": 4500, // 已完成预约数
    "cancelledAppointmentCount": 200 // 已取消预约数
  }
}
```

## 预约相关接口

### 1. 创建预约

**接口**：`POST /appointments`

**请求参数**：
```json
{
  "doctorId": "d10001", // 医生ID
  "slotId": "s10001", // 时间段ID
  "note": "初次检查" // 可选，预约备注
}
```

**响应**：
```json
{
  "code": 200,
  "message": "预约成功",
  "data": {
    "appointmentId": "a10001",
    "doctorName": "李医生",
    "hospital": "阳光口腔诊所",
    "address": "北京市海淀区中关村大街1号",
    "appointmentTime": "2023-06-15T14:30:00Z",
    "duration": 30
  }
}
```

### 2. 取消预约

**接口**：`PUT /appointments/{appointmentId}/cancel`

**请求参数**：
```json
{
  "reason": "时间冲突" // 可选，取消原因
}
```

**响应**：
```json
{
  "code": 200,
  "message": "预约取消成功",
  "data": null
}
```

### 3. 获取预约详情

**接口**：`GET /appointments/{appointmentId}`

**响应**：
```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "appointmentId": "a10001",
    "doctorId": "d10001",
    "doctorName": "李医生",
    "doctorAvatar": "头像URL",
    "specialty": "牙齿矫正",
    "hospital": "阳光口腔诊所",
    "address": "北京市海淀区中关村大街1号",
    "patientId": "p10001",
    "patientName": "张三",
    "patientAvatar": "头像URL",
    "appointmentTime": "2023-06-15T14:30:00Z",
    "duration": 30,
    "status": "confirmed",
    "note": "初次检查",
    "createdAt": "2023-06-10T09:15:00Z"
  }
}
```

## 评价相关接口

### 1. 提交医生评价

**接口**：`POST /ratings`

**请求参数**：
```json
{
  "doctorId": "d10001", // 医生ID
  "appointmentId": "a10001", // 预约ID
  "score": 5, // 评分，1-5
  "comment": "医生非常专业，服务态度很好" // 可选，评价内容
}
```

**响应**：
```json
{
  "code": 200,
  "message": "评价提交成功",
  "data": null
}
```

### 2. 获取医生评价列表

**接口**：`GET /doctors/{doctorId}/ratings`

**请求参数**：
```
page: 1 // 可选，页码，默认1
pageSize: 10 // 可选，每页数量，默认10
```

**响应**：
```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "total": 125,
    "averageScore": 4.8,
    "list": [
      {
        "ratingId": "r10001",
        "patientId": "p10001",
        "patientName": "张三",
        "patientAvatar": "头像URL",
        "score": 5,
        "comment": "医生非常专业，服务态度很好",
        "createdAt": "2023-06-16T15:30:00Z"
      }
      // ...更多评价
    ]
  }
}
```

## 通知相关接口

### 1. 获取用户通知列表

**接口**：`GET /notifications`

**请求参数**：
```
type: appointment // 可选，通知类型：appointment(预约相关)、system(系统通知)
isRead: false // 可选，是否已读
page: 1 // 可选，页码，默认1
pageSize: 10 // 可选，每页数量，默认10
```

**响应**：
```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "total": 15,
    "unreadCount": 5,
    "list": [
      {
        "notificationId": "n10001",
        "type": "appointment",
        "title": "预约提醒",
        "content": "您明天14:30在阳光口腔诊所与李医生的预约，请准时到达",
        "isRead": false,
        "createdAt": "2023-06-14T10:00:00Z",
        "relatedId": "a10001", // 相关ID，如预约ID
        "relatedType": "appointment" // 相关类型
      }
      // ...更多通知
    ]
  }
}
```

### 2. 标记通知为已读

**接口**：`PUT /notifications/read`

**请求参数**：
```json
{
  "notificationIds": ["n10001", "n10002"] // 通知ID数组，为空则标记所有为已读
}
```

**响应**：
```json
{
  "code": 200,
  "message": "标记成功",
  "data": null
}
```

### 3. 删除通知

**接口**：`DELETE /notifications/{notificationId}`

**响应**：
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

### 4. 批量删除通知

**接口**：`DELETE /notifications`

**请求参数**：
```json
{
  "notificationIds": ["n10001", "n10002"] // 通知ID数组
}
```

**响应**：
```json
{
  "code": 200,
  "message": "批量删除成功",
  "data": null
}
``` 