<template>
  <view class="container">
    <!-- 副标题 -->
    <view class="subtitle-section">
      <text class="subtitle">我们将智能为您分析并推荐医生</text>
    </view>

    <!-- 输入区域 -->
    <view class="input-section">
      <view class="input-container">
        <textarea
          class="situation-input"
          placeholder="请详细描述您的口腔问题或症状，例如：牙痛、牙龈出血、想要矫正牙齿等..."
          v-model="situationText"
          maxlength="500"
          auto-height
          :show-confirm-bar="false"
        ></textarea>
        
        <!-- 字数统计 -->
        <view class="char-count">
          <text class="count-text">{{ situationText.length }}/500</text>
        </view>
        
        <!-- 确定按钮 -->
        <view class="confirm-btn-container">
          <button 
            class="confirm-btn" 
            @tap="confirmSituation"
            :disabled="!situationText.trim()"
          >
            确定
          </button>
        </view>
      </view>
    </view>

    <!-- 常见问题快速选择 - 分析前显示 -->
    <view class="quick-select-section" v-if="!showAnalysis">
      <view class="quick-title">
        <text class="quick-title-text">常见问题快速选择</text>
      </view>
      <view class="quick-options">
        <view
          class="option-tag"
          :class="{ 'option-selected': isOptionSelected(option) }"
          v-for="(option, index) in quickOptions"
          :key="index"
          @tap="selectQuickOption(option)"
        >
          {{ option }}
          <text class="check-icon" v-if="isOptionSelected(option)">✓</text>
        </view>
      </view>
    </view>

    <!-- 专科推荐分析 - 分析后显示 -->
    <view class="specialty-recommendation-section" v-if="showAnalysis && recommendedSpecialties.length > 0">
      <view class="specialty-header">
        <text class="specialty-title">专科推荐分析</text>
        <text class="specialty-subtitle">根据您的情况描述智能分析</text>
      </view>

      <view class="analysis-content">
        <view class="analysis-summary">
          <text class="analysis-text">{{ analysisResult }}</text>
        </view>

        <view class="specialty-cards">
          <view
            class="specialty-card"
            v-for="(specialty, index) in recommendedSpecialties"
            :key="index"
          >
            <view class="specialty-info">
              <text class="specialty-name">{{ specialty.name }}</text>
              <text class="specialty-reason">{{ specialty.reason }}</text>
            </view>
            <view class="priority-badge" :class="'priority-' + specialty.priority">
              {{ specialty.priority === 'high' ? '优先推荐' : '建议就诊' }}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 推荐医生 - 分析后显示 -->
    <view class="recommended-doctors-section" v-if="showAnalysis && recommendedDoctors.length > 0">
      <view class="doctors-header">
        <text class="doctors-title">推荐医生</text>
        <text class="doctors-subtitle">基于您的情况和所在区域</text>
      </view>

      <view class="doctors-list">
        <view
          class="doctor-card"
          v-for="(doctor, index) in recommendedDoctors"
          :key="index"
          @tap="selectDoctor(doctor)"
        >
          <view class="doctor-avatar">
            <image :src="doctor.avatar" class="avatar-img" mode="aspectFill"></image>
          </view>
          <view class="doctor-info">
            <view class="doctor-basic">
              <text class="doctor-name">{{ doctor.name }}</text>
              <text class="doctor-title">{{ doctor.title }}</text>
            </view>
            <view class="doctor-specialty">
              <text class="specialty-text">{{ doctor.specialty }}</text>
            </view>
            <view class="doctor-hospital">
              <text class="hospital-text">{{ doctor.hospital }}</text>
              <text class="area-text">{{ doctor.area }}</text>
            </view>
            <view class="doctor-rating">
              <view class="rating-stars">
                <text class="star" v-for="i in 5" :key="i" :class="{ 'filled': i <= doctor.rating }">★</text>
              </view>
              <text class="rating-text">{{ doctor.rating }}.0</text>
              <text class="experience-text">{{ doctor.experience }}年经验</text>
            </view>
          </view>
          <view class="match-badge">
            <text class="match-text">{{ doctor.matchReason }}</text>
          </view>
        </view>
      </view>

      <view class="view-more-section">
        <button class="view-more-btn" @tap="viewAllDoctors">查看更多医生</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 情况描述文本
const situationText = ref('');

// 是否显示分析结果
const showAnalysis = ref(false);

// 分析结果文本
const analysisResult = ref('');

// 推荐专科
const recommendedSpecialties = ref([]);

// 推荐医生
const recommendedDoctors = ref([]);

// 用户选择的区域（从上一页传递或本地存储获取）
const selectedArea = ref('');

// 快速选择选项
const quickOptions = ref([
  '牙痛',
  '牙龈出血',
  '牙齿松动',
  '口臭',
  '牙齿矫正',
  '牙齿美白',
  '智齿问题',
  '补牙',
  '洗牙',
  '种植牙'
]);

// 选中的快速选项
const selectedQuickOptions = ref([]);

// 选择快速选项（多选）
const selectQuickOption = (option) => {
  const index = selectedQuickOptions.value.indexOf(option);
  if (index > -1) {
    // 如果已选中，则取消选中
    selectedQuickOptions.value.splice(index, 1);
  } else {
    // 如果未选中，则添加到选中列表
    selectedQuickOptions.value.push(option);
  }
};

// 检查选项是否被选中
const isOptionSelected = (option) => {
  return selectedQuickOptions.value.includes(option);
};

// 确认情况描述
const confirmSituation = () => {
  if (!situationText.value.trim() && selectedQuickOptions.value.length === 0) {
    uni.showToast({
      title: '请输入您的情况或选择问题',
      icon: 'none'
    });
    return;
  }

  console.log('开始分析 - 用户输入:', situationText.value);
  console.log('开始分析 - 选中选项:', selectedQuickOptions.value);
  console.log('开始分析 - 当前区域:', selectedArea.value);

  uni.showLoading({
    title: '正在分析...'
  });

  // 模拟分析过程
  setTimeout(() => {
    uni.hideLoading();

    // 进行智能分析
    performAnalysis(situationText.value);

    // 显示分析结果
    showAnalysis.value = true;

    // 滚动到分析结果区域
    setTimeout(() => {
      uni.pageScrollTo({
        selector: '.specialty-recommendation-section',
        duration: 500
      });
    }, 100);
  }, 1000);
};

// 执行智能分析
const performAnalysis = (input) => {
  // 合并用户输入和选中的快速选项
  const allInput = input.toLowerCase();
  const selectedOptions = selectedQuickOptions.value.join('、').toLowerCase();
  const combinedInput = allInput + '、' + selectedOptions;

  // 分析专科推荐
  const specialties = analyzeSpecialties(combinedInput);
  recommendedSpecialties.value = specialties;

  // 生成分析文本
  analysisResult.value = generateAnalysisText(combinedInput, specialties, input, selectedQuickOptions.value);

  // 根据专科推荐医生
  recommendDoctors(specialties);
};

// 分析专科推荐
const analyzeSpecialties = (input) => {
  const specialties = [];

  if (input.includes('痛') || input.includes('疼')) {
    specialties.push({
      name: '口腔内科',
      reason: '专业治疗牙痛、牙髓炎等疼痛问题',
      priority: 'high'
    });
    if (input.includes('智齿') || input.includes('拔')) {
      specialties.push({
        name: '口腔外科',
        reason: '处理复杂拔牙和外科手术',
        priority: 'medium'
      });
    }
  } else if (input.includes('出血') || input.includes('牙龈') || input.includes('肿')) {
    specialties.push({
      name: '牙周病科',
      reason: '专业治疗牙龈出血、牙周炎等问题',
      priority: 'high'
    });
  } else if (input.includes('矫正') || input.includes('整齐') || input.includes('排列') || input.includes('歪')) {
    specialties.push({
      name: '口腔正畸科',
      reason: '专业进行牙齿矫正和排列调整',
      priority: 'high'
    });
  } else if (input.includes('美白') || input.includes('黄') || input.includes('白') || input.includes('美容')) {
    specialties.push({
      name: '口腔美容科',
      reason: '专业提供牙齿美白和美容服务',
      priority: 'high'
    });
  } else if (input.includes('智齿')) {
    specialties.push({
      name: '口腔外科',
      reason: '专业处理智齿拔除和相关问题',
      priority: 'high'
    });
  } else if (input.includes('种植') || input.includes('缺牙') || input.includes('假牙')) {
    specialties.push({
      name: '口腔种植科',
      reason: '专业进行种植牙和牙齿修复',
      priority: 'high'
    });
    specialties.push({
      name: '口腔修复科',
      reason: '提供多种牙齿修复方案',
      priority: 'medium'
    });
  } else if (input.includes('补牙') || input.includes('蛀牙') || input.includes('龋齿') || input.includes('洞')) {
    specialties.push({
      name: '口腔内科',
      reason: '专业治疗龋齿和进行补牙',
      priority: 'high'
    });
  } else if (input.includes('洗牙') || input.includes('清洁') || input.includes('结石')) {
    specialties.push({
      name: '牙周病科',
      reason: '专业进行洗牙和口腔清洁',
      priority: 'high'
    });
  } else {
    specialties.push({
      name: '口腔综合科',
      reason: '进行全面检查，确定具体治疗方案',
      priority: 'high'
    });
  }

  return specialties.slice(0, 2); // 最多返回2个专科
};

// 生成分析文本
const generateAnalysisText = (combinedInput, specialties, userInput, selectedOptions) => {
  let text = '根据您的';

  // 如果有用户输入和选中选项，都要提及
  if (userInput.trim() && selectedOptions.length > 0) {
    text += '情况描述和选择的问题';
  } else if (userInput.trim()) {
    text += '情况描述';
  } else if (selectedOptions.length > 0) {
    text += '选择的问题';
  } else {
    text += '情况';
  }

  text += '分析：';

  // 分析主要问题
  const problems = [];
  if (combinedInput.includes('痛') || combinedInput.includes('疼')) {
    problems.push('牙痛问题');
  }
  if (combinedInput.includes('出血') || combinedInput.includes('牙龈')) {
    problems.push('牙龈问题');
  }
  if (combinedInput.includes('矫正')) {
    problems.push('牙齿矫正需求');
  }
  if (combinedInput.includes('美白')) {
    problems.push('牙齿美白需求');
  }
  if (combinedInput.includes('智齿')) {
    problems.push('智齿问题');
  }
  if (combinedInput.includes('种植')) {
    problems.push('种植牙需求');
  }
  if (combinedInput.includes('补牙') || combinedInput.includes('蛀牙')) {
    problems.push('补牙治疗需求');
  }
  if (combinedInput.includes('洗牙') || combinedInput.includes('清洁')) {
    problems.push('口腔清洁需求');
  }

  if (problems.length > 0) {
    text += `您可能存在${problems.join('、')}，`;
  }

  // 显示选中的快速选项
  if (selectedOptions.length > 0) {
    text += `特别关注：${selectedOptions.join('、')}。`;
  }

  if (specialties.length > 0) {
    text += `建议优先就诊${specialties[0].name}`;
    if (specialties.length > 1) {
      text += `，也可考虑${specialties[1].name}`;
    }
    text += '。';
  }

  return text;
};

// 推荐医生
const recommendDoctors = (specialties) => {
  console.log('=== 开始推荐医生 ===');
  console.log('推荐专科:', specialties);
  console.log('当前用户区域:', selectedArea.value);

  // 模拟医生数据，实际应该从API获取
  const allDoctors = [
    // 漳州市芗城区医生
    {
      id: 1,
      name: '张志强',
      title: '主任医师',
      specialty: '口腔内科',
      hospital: '漳州市医院',
      area: '漳州市芗城区',
      avatar: '/static/images/doctor1.jpg',
      rating: 5,
      experience: 18,
      matchReason: '专业对口'
    },
    {
      id: 2,
      name: '李美华',
      title: '副主任医师',
      specialty: '牙周病科',
      hospital: '漳州市第一医院',
      area: '漳州市芗城区',
      avatar: '/static/images/doctor2.jpg',
      rating: 5,
      experience: 15,
      matchReason: '经验丰富'
    },
    {
      id: 3,
      name: '王建国',
      title: '主治医师',
      specialty: '口腔正畸科',
      hospital: '漳州市中医院',
      area: '漳州市芗城区',
      avatar: '/static/images/doctor3.jpg',
      rating: 4,
      experience: 12,
      matchReason: '技术精湛'
    },
    {
      id: 4,
      name: '陈丽萍',
      title: '主任医师',
      specialty: '口腔外科',
      hospital: '漳州市人民医院',
      area: '漳州市芗城区',
      avatar: '/static/images/doctor4.jpg',
      rating: 5,
      experience: 20,
      matchReason: '权威专家'
    },
    {
      id: 5,
      name: '林晓东',
      title: '副主任医师',
      specialty: '口腔种植科',
      hospital: '漳州口腔专科医院',
      area: '漳州市芗城区',
      avatar: '/static/images/doctor5.jpg',
      rating: 4,
      experience: 14,
      matchReason: '专业对口'
    },
    {
      id: 6,
      name: '黄雅琴',
      title: '主治医师',
      specialty: '口腔美容科',
      hospital: '漳州美齿口腔诊所',
      area: '漳州市芗城区',
      avatar: '/static/images/doctor6.jpg',
      rating: 4,
      experience: 10,
      matchReason: '美容专家'
    },

    // 福州市鼓楼区医生
    {
      id: 7,
      name: '郑文斌',
      title: '主任医师',
      specialty: '口腔内科',
      hospital: '福建省立医院',
      area: '福州市鼓楼区',
      avatar: '/static/images/doctor7.jpg',
      rating: 5,
      experience: 22,
      matchReason: '治疗专家'
    },
    {
      id: 8,
      name: '吴秀兰',
      title: '副主任医师',
      specialty: '牙周病科',
      hospital: '福州市第一医院',
      area: '福州市鼓楼区',
      avatar: '/static/images/doctor8.jpg',
      rating: 5,
      experience: 16,
      matchReason: '牙周专家'
    },
    {
      id: 9,
      name: '刘志华',
      title: '主治医师',
      specialty: '口腔正畸科',
      hospital: '福建医科大学附属口腔医院',
      area: '福州市鼓楼区',
      avatar: '/static/images/doctor9.jpg',
      rating: 4,
      experience: 11,
      matchReason: '矫正专家'
    },
    {
      id: 10,
      name: '许明辉',
      title: '主任医师',
      specialty: '口腔外科',
      hospital: '福州市中医院',
      area: '福州市鼓楼区',
      avatar: '/static/images/doctor10.jpg',
      rating: 5,
      experience: 19,
      matchReason: '外科专家'
    },
    {
      id: 11,
      name: '谢丽娟',
      title: '副主任医师',
      specialty: '口腔种植科',
      hospital: '福州口腔医院',
      area: '福州市鼓楼区',
      avatar: '/static/images/doctor11.jpg',
      rating: 4,
      experience: 13,
      matchReason: '种植专家'
    },
    {
      id: 12,
      name: '蔡雅芳',
      title: '主治医师',
      specialty: '口腔美容科',
      hospital: '福州美齿口腔中心',
      area: '福州市鼓楼区',
      avatar: '/static/images/doctor12.jpg',
      rating: 4,
      experience: 8,
      matchReason: '美容专家'
    }
  ];

  console.log('所有医生数据:', allDoctors.map(d => `${d.name}(${d.area}-${d.specialty})`));

  // 智能推荐医生算法 - 优先级排序
  const targetSpecialties = specialties.map(s => s.name);
  const userArea = selectedArea.value || '漳州市芗城区'; // 默认漳州市芗城区

  console.log('=== 智能推荐医生算法 ===');
  console.log('推荐医生 - 目标专科:', targetSpecialties);
  console.log('推荐医生 - 用户区域:', userArea);

  // 按优先级分组医生
  // 优先级1: 区域一致 + 专科一致 (最高优先级)
  const priority1Doctors = allDoctors.filter(doctor => {
    const isAreaMatch = doctor.area === userArea;
    const isSpecialtyMatch = targetSpecialties.includes(doctor.specialty);
    const isPriority1 = isAreaMatch && isSpecialtyMatch;

    if (isPriority1) {
      console.log(`✅ 优先级1: ${doctor.name} - ${doctor.area} - ${doctor.specialty}`);
    }

    return isPriority1;
  });

  // 优先级2: 区域一致 + 专科不一致
  const priority2Doctors = allDoctors.filter(doctor => {
    const isAreaMatch = doctor.area === userArea;
    const isSpecialtyMatch = targetSpecialties.includes(doctor.specialty);
    const isPriority2 = isAreaMatch && !isSpecialtyMatch;

    if (isPriority2) {
      console.log(`⚠️ 优先级2: ${doctor.name} - ${doctor.area} - ${doctor.specialty}`);
    }

    return isPriority2;
  });

  // 优先级3: 区域不一致 + 专科一致
  const priority3Doctors = allDoctors.filter(doctor => {
    const isAreaMatch = doctor.area === userArea;
    const isSpecialtyMatch = targetSpecialties.includes(doctor.specialty);
    const isPriority3 = !isAreaMatch && isSpecialtyMatch;

    if (isPriority3) {
      console.log(`🔄 优先级3: ${doctor.name} - ${doctor.area} - ${doctor.specialty}`);
    }

    return isPriority3;
  });

  // 优先级4: 区域不一致 + 专科不一致 (最低优先级，一般不推荐)
  const priority4Doctors = allDoctors.filter(doctor => {
    const isAreaMatch = doctor.area === userArea;
    const isSpecialtyMatch = targetSpecialties.includes(doctor.specialty);
    const isPriority4 = !isAreaMatch && !isSpecialtyMatch;

    return isPriority4;
  });

  console.log(`优先级1医生数量: ${priority1Doctors.length}位`);
  console.log(`优先级2医生数量: ${priority2Doctors.length}位`);
  console.log(`优先级3医生数量: ${priority3Doctors.length}位`);
  console.log(`优先级4医生数量: ${priority4Doctors.length}位`);

  // 对每个优先级内的医生按评分和经验排序
  const sortDoctors = (doctors) => {
    return doctors.sort((a, b) => {
      // 按评分排序
      if (a.rating !== b.rating) return b.rating - a.rating;
      // 评分相同时按经验排序
      return b.experience - a.experience;
    });
  };

  // 按优先级合并医生列表
  const sortedDoctors = [
    ...sortDoctors([...priority1Doctors]),
    ...sortDoctors([...priority2Doctors]),
    ...sortDoctors([...priority3Doctors])
    // 不包含优先级4，因为区域和专科都不匹配的医生不应该推荐
  ];

  recommendedDoctors.value = sortedDoctors.slice(0, 3); // 推荐前3个医生

  console.log('最终推荐医生:');
  recommendedDoctors.value.forEach((doctor, index) => {
    const isAreaMatch = doctor.area === userArea;
    const isSpecialtyMatch = targetSpecialties.includes(doctor.specialty);
    let priority = '';
    if (isAreaMatch && isSpecialtyMatch) priority = '优先级1(区域✅专科✅)';
    else if (isAreaMatch && !isSpecialtyMatch) priority = '优先级2(区域✅专科❌)';
    else if (!isAreaMatch && isSpecialtyMatch) priority = '优先级3(区域❌专科✅)';
    else priority = '优先级4(区域❌专科❌)';

    console.log(`${index + 1}. ${doctor.name} - ${doctor.area} - ${doctor.specialty} - ${priority}`);
  });
  console.log('=== 推荐医生完成 ===');
};

// 选择医生
const selectDoctor = (doctor) => {
  // 跳转到医生详情或预约页面
  uni.navigateTo({
    url: `/pages/doctor-selection/index?doctorId=${doctor.id}&specialty=${doctor.specialty}`
  });
};

// 查看更多医生
const viewAllDoctors = () => {
  const specialtyNames = recommendedSpecialties.value.map(s => s.name).join(',');
  uni.navigateTo({
    url: `/pages/doctor-selection/index?specialties=${specialtyNames}&area=${selectedArea.value}`
  });
};

// 页面加载时获取用户区域
onMounted(() => {
  // 从本地存储获取用户在预约就医页面选择的地址
  const userAddress = uni.getStorageSync('userAddress');
  console.log('从本地存储获取的地址:', userAddress);

  if (userAddress) {
    selectedArea.value = userAddress;
  } else {
    // 如果没有选择地址，使用默认值
    selectedArea.value = '漳州市芗城区';
  }

  console.log('当前用户选择的区域:', selectedArea.value);

  // 检查所有医生的区域信息
  console.log('所有医生的区域信息:');
  const allDoctors = [
    // 漳州市芗城区医生
    {
      id: 1,
      name: '张志强',
      title: '主任医师',
      specialty: '口腔内科',
      hospital: '漳州市医院',
      area: '漳州市芗城区',
      avatar: '/static/images/doctor1.jpg',
      rating: 5,
      experience: 18,
      matchReason: '专业对口'
    },
    {
      id: 2,
      name: '李美华',
      title: '副主任医师',
      specialty: '牙周病科',
      hospital: '漳州市第一医院',
      area: '漳州市芗城区',
      avatar: '/static/images/doctor2.jpg',
      rating: 5,
      experience: 15,
      matchReason: '经验丰富'
    }
  ];

  allDoctors.forEach(doctor => {
    console.log(`医生: ${doctor.name}, 区域: "${doctor.area}", 专科: ${doctor.specialty}`);
  });
});
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 30rpx;
  box-sizing: border-box;
}

/* 副标题 */
.subtitle-section {
  text-align: center;
  margin-bottom: 40rpx;
  padding-top: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 输入区域 */
.input-section {
  margin-bottom: 40rpx;
}

.input-container {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.situation-input {
  width: 100%;
  min-height: 300rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  background-color: transparent;
  border: none;
  outline: none;
  resize: none;
  box-sizing: border-box;
}

.situation-input::placeholder {
  color: #999;
}

.char-count {
  text-align: right;
  margin-top: 15rpx;
  margin-bottom: 25rpx;
}

.count-text {
  font-size: 24rpx;
  color: #999;
}

.confirm-btn-container {
  display: flex;
  justify-content: flex-end;
}

.confirm-btn {
  background-color: #4a90e2;
  color: #ffffff;
  font-size: 28rpx;
  border: none;
  border-radius: 25rpx;
  padding: 15rpx 40rpx;
  transition: all 0.3s ease;
}

.confirm-btn:disabled {
  background-color: #ccc;
  color: #999;
}

.confirm-btn:active:not(:disabled) {
  background-color: #3a80d2;
  transform: scale(0.98);
}

/* 快速选择区域 */
.quick-select-section {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.quick-title {
  margin-bottom: 20rpx;
}

.quick-title-text {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.quick-options {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.option-tag {
  background-color: #f0f0f0;
  color: #666;
  font-size: 26rpx;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  border: 1rpx solid #e0e0e0;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.option-tag:active {
  transform: scale(0.98);
}

.option-tag.option-selected {
  background-color: #e6e6fa;
  color: #6a5acd;
  border-color: #6a5acd;
  font-weight: 500;
}

.check-icon {
  margin-left: 8rpx;
  font-size: 20rpx;
  font-weight: bold;
}

/* 专科推荐分析样式 */
.specialty-recommendation-section {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.specialty-header {
  margin-bottom: 25rpx;
}

.specialty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.specialty-subtitle {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.analysis-content {
  margin-top: 20rpx;
}

.analysis-summary {
  background-color: #f8f9ff;
  border-radius: 15rpx;
  padding: 20rpx;
  margin-bottom: 25rpx;
  border-left: 6rpx solid #4a90e2;
}

.analysis-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

.specialty-cards {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.specialty-card {
  background-color: #ffffff;
  border: 2rpx solid #e8f4fd;
  border-radius: 15rpx;
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

.specialty-info {
  flex: 1;
}

.specialty-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.specialty-reason {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.priority-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.priority-high {
  background-color: #ff6b6b;
  color: #ffffff;
}

.priority-medium {
  background-color: #ffa726;
  color: #ffffff;
}

/* 推荐医生样式 */
.recommended-doctors-section {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.doctors-header {
  margin-bottom: 25rpx;
}

.doctors-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.doctors-subtitle {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.doctors-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.doctor-card {
  background-color: #ffffff;
  border: 2rpx solid #f0f0f0;
  border-radius: 15rpx;
  padding: 25rpx;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  position: relative;
}

.doctor-card:active {
  background-color: #f8f9ff;
  border-color: #4a90e2;
  transform: scale(0.98);
}

.doctor-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
  background-color: #f0f0f0;
}

.avatar-img {
  width: 100%;
  height: 100%;
}

.doctor-info {
  flex: 1;
}

.doctor-basic {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.doctor-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-right: 15rpx;
}

.doctor-title {
  font-size: 24rpx;
  color: #666;
  background-color: #f0f0f0;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
}

.doctor-specialty {
  margin-bottom: 8rpx;
}

.specialty-text {
  font-size: 26rpx;
  color: #4a90e2;
  font-weight: 500;
}

.doctor-hospital {
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.hospital-text {
  font-size: 24rpx;
  color: #666;
  margin-right: 15rpx;
}

.area-text {
  font-size: 22rpx;
  color: #4a90e2;
  background-color: #f0f8ff;
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
  border: 1rpx solid #e6f3ff;
}

.doctor-rating {
  display: flex;
  align-items: center;
}

.rating-stars {
  margin-right: 10rpx;
}

.star {
  font-size: 24rpx;
  color: #ddd;
  margin-right: 2rpx;
}

.star.filled {
  color: #ffa726;
}

.rating-text {
  font-size: 24rpx;
  color: #333;
  margin-right: 15rpx;
}

.experience-text {
  font-size: 24rpx;
  color: #666;
}

.match-badge {
  position: absolute;
  top: 15rpx;
  right: 15rpx;
  background-color: #4a90e2;
  color: #ffffff;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 15rpx;
}

.match-text {
  font-size: 20rpx;
}

.view-more-section {
  margin-top: 25rpx;
  text-align: center;
}

.view-more-btn {
  background-color: #f8f9fa;
  color: #4a90e2;
  font-size: 28rpx;
  border: 2rpx solid #4a90e2;
  border-radius: 25rpx;
  padding: 15rpx 40rpx;
  transition: all 0.3s ease;
}

.view-more-btn:active {
  background-color: #4a90e2;
  color: #ffffff;
}
</style>
