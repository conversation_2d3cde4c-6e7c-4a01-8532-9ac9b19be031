# 口腔健康预约系统开发指南

## 前后端交互流程

### 1. 用户认证流程

```
前端                                后端
  |                                  |
  |------ 发送登录请求 ------------>|
  |                                  |-- 验证用户身份
  |                                  |-- 生成JWT令牌
  |<----- 返回JWT令牌 --------------|
  |                                  |
  |-- 存储令牌到localStorage         |
  |                                  |
  |------ 后续请求携带令牌 -------->|
  |                                  |-- 验证令牌
  |<----- 返回请求数据 -------------|
  |                                  |
```

### 2. 预约流程

```
患者端                              后端                              医生端
  |                                  |                                  |
  |-- 查询医生列表 ---------------->|                                  |
  |<- 返回推荐医生 ----------------|                                  |
  |                                  |                                  |
  |-- 查询医生空闲时间 ------------>|                                  |
  |<- 返回可用时间段 ---------------|                                  |
  |                                  |                                  |
  |-- 提交预约请求 ---------------->|                                  |
  |                                  |-- 锁定时间段                     |
  |                                  |-- 创建预约记录                   |
  |                                  |-- 发送通知 ------------------->|
  |<- 返回预约确认 ----------------|                                  |
  |                                  |                                  |
```

### 3. 通知流程

```
系统                                后端                              用户端
  |                                  |                                  |
  |-- 触发通知事件 ---------------->|                                  |
  |  (如预约提醒)                    |-- 创建通知记录                   |
  |                                  |-- 发送推送通知/邮件 ----------->|
  |                                  |                                  |
  |                                  |<- 用户查询通知列表 -------------|
  |                                  |-- 返回通知数据                   |
  |                                  |---------------------------- ---->|
  |                                  |                                  |
  |                                  |<- 标记通知已读 ----------------|
  |                                  |-- 更新通知状态                   |
  |                                  |---------------------------- ---->|
```

## 开发规范

### 1. API请求规范

#### 请求格式

- 使用RESTful风格API
- 请求头需包含：
  ```
  Content-Type: application/json
  Authorization: Bearer {token}  // 除登录注册等接口外
  ```
- GET请求参数使用URL查询参数
- POST/PUT请求数据使用JSON格式

#### 响应格式

所有API响应均为JSON格式，基本结构如下：

```json
{
  "code": 200,        // 状态码：200成功，非200表示错误
  "message": "成功",   // 状态描述
  "data": {}          // 响应数据，错误时可能为null或错误详情
}
```

#### 状态码规范

| 状态码 | 说明 |
| --- | --- |
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权（未登录或token无效） |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突（如时间段已被预约） |
| 500 | 服务器内部错误 |

### 2. 前端开发规范

#### 目录结构

```
src/
  ├── api/              # API请求封装
  ├── assets/           # 静态资源
  ├── components/       # 公共组件
  ├── pages/            # 页面组件
  ├── router/           # 路由配置
  ├── store/            # 状态管理
  ├── utils/            # 工具函数
  ├── App.vue           # 根组件
  └── main.js           # 入口文件
```

#### 命名规范

- 文件名：kebab-case（如user-info.vue）
- 组件名：PascalCase（如UserInfo）
- 变量/函数名：camelCase（如getUserInfo）
- 常量：UPPER_SNAKE_CASE（如API_BASE_URL）

#### 状态管理

建议使用Vuex或Pinia进行状态管理，主要管理：
- 用户信息和认证状态
- 全局UI状态（如加载状态、弹窗状态）
- 缓存数据（如医生列表、预约列表）

### 3. 后端开发规范

#### 目录结构

```
server/
  ├── config/           # 配置文件
  ├── controllers/      # 控制器
  ├── middlewares/      # 中间件
  ├── models/           # 数据模型
  ├── routes/           # 路由定义
  ├── services/         # 业务逻辑
  ├── utils/            # 工具函数
  └── app.js            # 入口文件
```

#### 代码规范

- 使用异步/await处理异步操作
- 统一错误处理
- 参数验证
- 日志记录

#### 安全措施

- 密码加密存储（如bcrypt）
- JWT令牌认证
- 请求参数验证
- CORS配置
- 防SQL注入

## 关键功能实现指南

### 1. 基于地理位置的医生推荐

#### 前端实现

```javascript
// 获取用户位置
async function getUserLocation() {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('浏览器不支持地理位置'));
      return;
    }
    
    navigator.geolocation.getCurrentPosition(
      position => {
        const location = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude
        };
        // 上传位置到后端
        api.updateUserLocation(location)
          .then(() => resolve(location))
          .catch(err => reject(err));
      },
      error => {
        reject(error);
      }
    );
  });
}

// 获取推荐医生
async function getRecommendedDoctors(specialty = '', maxDistance = 10) {
  try {
    // 先获取用户位置
    await getUserLocation();
    
    // 请求推荐医生列表
    const response = await api.getRecommendedDoctors({
      specialty,
      maxDistance,
      page: 1,
      pageSize: 10
    });
    
    return response.data.list;
  } catch (error) {
    console.error('获取推荐医生失败', error);
    throw error;
  }
}
```

#### 后端实现（伪代码）

```javascript
// 计算两点之间距离（Haversine公式）
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371; // 地球半径（公里）
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

// 获取推荐医生
async function getRecommendedDoctors(userId, specialty, maxDistance, page, pageSize) {
  // 获取患者位置
  const patient = await PatientModel.findOne({ patient_id: userId });
  if (!patient || !patient.latitude || !patient.longitude) {
    throw new Error('患者位置信息不存在');
  }
  
  // 查询医生
  let query = {};
  if (specialty) {
    query.specialty = specialty;
  }
  
  const doctors = await DoctorModel.find(query);
  
  // 计算距离并筛选
  const doctorsWithDistance = doctors.map(doctor => {
    const distance = calculateDistance(
      patient.latitude, patient.longitude,
      doctor.latitude, doctor.longitude
    );
    return { ...doctor.toObject(), distance };
  }).filter(doctor => doctor.distance <= maxDistance);
  
  // 按距离排序
  doctorsWithDistance.sort((a, b) => a.distance - b.distance);
  
  // 分页
  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  const result = doctorsWithDistance.slice(start, end);
  
  return {
    total: doctorsWithDistance.length,
    list: result
  };
}
```

### 2. 预约提醒实现

#### 定时任务（后端）

```javascript
// 使用node-schedule定时任务
const schedule = require('node-schedule');

// 每小时执行一次，检查需要发送提醒的预约
schedule.scheduleJob('0 * * * *', async function() {
  try {
    const now = new Date();
    const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);
    const oneDayLater = new Date(now.getTime() + 24 * 60 * 60 * 1000);
    
    // 查找1小时后的预约
    const appointmentsOneHour = await AppointmentModel.find({
      appointment_time: {
        $gte: now,
        $lte: oneHourLater
      },
      status: 'confirmed'
    }).populate('patient_id doctor_id');
    
    // 查找1天后的预约
    const appointmentsOneDay = await AppointmentModel.find({
      appointment_time: {
        $gte: oneHourLater,
        $lte: oneDayLater
      },
      status: 'confirmed'
    }).populate('patient_id doctor_id');
    
    // 发送1小时提醒
    for (const appointment of appointmentsOneHour) {
      await sendAppointmentReminder(appointment, '1小时');
    }
    
    // 发送1天提醒
    for (const appointment of appointmentsOneDay) {
      await sendAppointmentReminder(appointment, '1天');
    }
  } catch (error) {
    console.error('预约提醒任务失败', error);
  }
});

// 发送预约提醒
async function sendAppointmentReminder(appointment, timeType) {
  // 创建通知记录
  await NotificationModel.create({
    user_id: appointment.patient_id.user_id,
    type: 'appointment',
    title: '预约提醒',
    content: `您${timeType}后(${formatDateTime(appointment.appointment_time)})在${appointment.doctor_id.hospital}与${appointment.doctor_id.name}医生的预约，请准时到达`,
    related_id: appointment._id,
    related_type: 'appointment'
  });
  
  // 发送邮件
  if (appointment.patient_id.email) {
    await sendEmail(
      appointment.patient_id.email,
      '口腔健康预约提醒',
      `您${timeType}后(${formatDateTime(appointment.appointment_time)})在${appointment.doctor_id.hospital}与${appointment.doctor_id.name}医生的预约，请准时到达`
    );
  }
  
  // 发送短信
  if (appointment.patient_id.phone) {
    await sendSMS(
      appointment.patient_id.phone,
      `【口腔健康】您${timeType}后(${formatDateTime(appointment.appointment_time)})在${appointment.doctor_id.hospital}与${appointment.doctor_id.name}医生的预约，请准时到达`
    );
  }
  
  // 同样为医生创建通知
  await NotificationModel.create({
    user_id: appointment.doctor_id.user_id,
    type: 'appointment',
    title: '预约提醒',
    content: `您${timeType}后(${formatDateTime(appointment.appointment_time)})有患者${appointment.patient_id.name}的预约`,
    related_id: appointment._id,
    related_type: 'appointment'
  });
}
```

### 3. 用户认证实现

#### JWT认证（后端）

```javascript
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');

// 登录认证
async function login(account, password, userType) {
  // 查找用户
  const user = await UserModel.findOne({
    $or: [{ phone: account }, { email: account }],
    user_type: userType
  });
  
  if (!user) {
    throw new Error('用户不存在');
  }
  
  // 验证密码
  const isMatch = await bcrypt.compare(password, user.password);
  if (!isMatch) {
    throw new Error('密码错误');
  }
  
  // 生成JWT令牌
  const token = jwt.sign(
    { userId: user.user_id, userType: user.user_type },
    process.env.JWT_SECRET,
    { expiresIn: '7d' }
  );
  
  return {
    userId: user.user_id,
    userType: user.user_type,
    name: user.name,
    avatar: user.avatar,
    token
  };
}

// JWT验证中间件
function authMiddleware(req, res, next) {
  try {
    // 获取请求头中的token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        code: 401,
        message: '未授权',
        data: null
      });
    }
    
    const token = authHeader.split(' ')[1];
    
    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // 将用户信息添加到请求对象
    req.user = {
      userId: decoded.userId,
      userType: decoded.userType
    };
    
    next();
  } catch (error) {
    return res.status(401).json({
      code: 401,
      message: '无效的令牌',
      data: null
    });
  }
}
```

## 测试指南

### 1. 单元测试

推荐使用Jest进行单元测试，主要测试：
- API请求函数
- 工具函数
- 组件渲染

### 2. 接口测试

推荐使用Postman或Jest+Supertest进行接口测试，测试流程：
1. 用户认证流程
2. 医生推荐功能
3. 预约创建和取消
4. 评价提交和查询

### 3. 端到端测试

推荐使用Cypress进行端到端测试，测试场景：
1. 用户注册登录
2. 医生搜索和筛选
3. 预约流程
4. 评价提交

## 部署指南

### 1. 前端部署

1. 构建生产环境代码：
   ```
   npm run build
   ```

2. 将构建产物部署到Web服务器（如Nginx）

3. 配置Nginx：
   ```nginx
   server {
     listen 80;
     server_name your-domain.com;
     
     location / {
       root /path/to/dist;
       index index.html;
       try_files $uri $uri/ /index.html;
     }
     
     location /api/ {
       proxy_pass http://backend-server:3000/;
       proxy_set_header Host $host;
       proxy_set_header X-Real-IP $remote_addr;
     }
   }
   ```

### 2. 后端部署

1. 准备环境变量：
   ```
   NODE_ENV=production
   PORT=3000
   DB_URI=********************************:port/database
   JWT_SECRET=your-secret-key
   SMTP_HOST=smtp.example.com
   SMTP_PORT=587
   SMTP_USER=<EMAIL>
   SMTP_PASS=your-password
   SMS_API_KEY=your-sms-api-key
   ```

2. 安装依赖并启动：
   ```
   npm install --production
   npm start
   ```

3. 使用PM2管理进程：
   ```
   pm2 start app.js --name dental-api
   ```

### 3. 数据库部署

1. 安装MongoDB或MySQL

2. 创建数据库和用户

3. 导入初始数据（如有）

4. 配置备份策略

## 性能优化建议

### 1. 前端优化

- 路由懒加载
- 组件按需加载
- 图片懒加载和压缩
- 本地缓存（localStorage/IndexedDB）
- 服务端渲染（SSR）或静态生成（SSG）

### 2. 后端优化

- 数据库索引优化
- 查询缓存（Redis）
- 分页查询
- 异步处理耗时操作
- 负载均衡

### 3. 网络优化

- CDN加速静态资源
- HTTP/2
- Gzip压缩
- 减少HTTP请求
- API请求合并 