<template>
  <view class="container">
    <view class="content">
      <!-- 搜索栏 -->
      <view class="search-bar">
        <input class="search-input" v-model="searchQuery" placeholder="搜索医生姓名" @input="handleSearch" />
        <text class="search-icon">🔍</text>
      </view>
      
      <!-- 医生列表 -->
      <view class="doctor-list">
        <view v-if="filteredDoctors.length === 0" class="empty-state">
          <text class="empty-icon">🔍</text>
          <text class="empty-text">暂无医生信息</text>
        </view>
        
        <view v-for="(doctor, index) in filteredDoctors" :key="index" class="doctor-card">
          <view class="doctor-info">
            <view class="doctor-avatar">
              <text class="avatar-text">{{ doctor.name ? doctor.name.substring(0, 1) : '医' }}</text>
            </view>
            <view class="doctor-details">
              <text class="doctor-name">{{ doctor.name }}</text>
            </view>
          </view>
          
          <view class="doctor-actions">
            <button class="action-button edit" @tap="viewDoctor(doctor)">查看</button>
            <button class="action-button delete" @tap="deleteDoctor(doctor)">删除</button>
          </view>
        </view>
      </view>
      
      <!-- 添加医生按钮 -->
      <view class="add-button-container">
        <button class="add-button" @tap="addDoctor">+ 添加医生</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 搜索查询
const searchQuery = ref('');

// 医生列表
const doctors = ref([
  {
    id: 'D10001',
    name: '李医生',
    gender: '男',
    phone: '13800138000',
    email: '<EMAIL>',
    clinicLocation: '北京市朝阳区健康路88号',
    specialization: '口腔正畸科',
    title: '主治医师'
  },
  {
    id: 'D10002',
    name: '王医生',
    gender: '女',
    phone: '13900139000',
    email: '<EMAIL>',
    clinicLocation: '北京市海淀区医疗路66号',
    specialization: '牙体牙髓科',
    title: '副主任医师'
  },
  {
    id: 'D10003',
    name: '张医生',
    gender: '男',
    phone: '13700137000',
    email: '<EMAIL>',
    clinicLocation: '北京市西城区口腔路123号',
    specialization: '口腔修复科',
    title: '主任医师'
  }
]);

// 筛选后的医生列表
const filteredDoctors = computed(() => {
  if (!searchQuery.value) {
    return doctors.value;
  }

  const query = searchQuery.value.toLowerCase();
  return doctors.value.filter(doctor =>
    doctor.name.toLowerCase().includes(query)
  );
});

// 页面加载时
onMounted(() => {
  // 获取医生列表
  // 实际应用中应该从API获取
});

// 搜索处理
const handleSearch = () => {
  // 实际应用中可能需要调用API进行搜索
};

// 添加医生
const addDoctor = () => {
  uni.navigateTo({
    url: '/pages/doctor-form/index?mode=add'
  });
};

// 查看医生信息
const viewDoctor = (doctor) => {
  // 保存当前选中的医生信息
  try {
    uni.setStorageSync('currentViewDoctor', doctor);

    // 跳转到查看页面
    uni.navigateTo({
      url: `/pages/doctor-form/index?mode=view&id=${doctor.id}`
    });
  } catch (e) {
    console.error('保存医生信息失败', e);
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none'
    });
  }
};

// 删除医生
const deleteDoctor = (doctor) => {
  uni.showModal({
    title: '确认删除',
    content: `确定要删除医生 ${doctor.name}(${doctor.id}) 的账号吗？此操作不可恢复。`,
    success: (res) => {
      if (res.confirm) {
        // 实际应用中应该调用API删除医生
        const index = doctors.value.findIndex(d => d.id === doctor.id);
        if (index !== -1) {
          doctors.value.splice(index, 1);
          
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          });
        }
      }
    }
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
}



.content {
  flex: 1;
  padding: 30rpx;
}

/* 搜索栏样式 */
.search-bar {
  background-color: #fff;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  padding-right: 60rpx;
}

.search-icon {
  font-size: 36rpx;
  position: absolute;
  right: 30rpx;
  color: #999;
}

/* 医生列表样式 */
.doctor-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 100rpx;
}

.empty-state {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
}

.doctor-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.doctor-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.doctor-avatar {
  width: 100rpx;
  height: 100rpx;
  background-color: #8e99f3;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
}

.avatar-text {
  font-size: 40rpx;
  color: #fff;
  font-weight: bold;
}

.doctor-details {
  display: flex;
  flex-direction: column;
}

.doctor-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 6rpx;
}

.doctor-id {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 6rpx;
}

.doctor-department {
  font-size: 26rpx;
  color: #666;
  background-color: rgba(142, 153, 243, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  display: inline-block;
}

.doctor-actions {
  display: flex;
  gap: 20rpx;
}

.action-button {
  font-size: 24rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  border: none;
  line-height: 1.8;
}

.action-button.edit {
  background-color: #8e99f3;
  color: #fff;
}

.action-button.delete {
  background-color: #ff6b6b;
  color: #fff;
}

/* 添加按钮样式 */
.add-button-container {
  position: fixed;
  bottom: 30rpx;
  left: 30rpx;
  right: 30rpx;
}

.add-button {
  background-color: #8e99f3;
  color: #fff;
  font-size: 32rpx;
  padding: 25rpx 0;
  border-radius: 50rpx;
  border: none;
  box-shadow: 0 6rpx 12rpx rgba(142, 153, 243, 0.3);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
    box-shadow: 0 3rpx 6rpx rgba(142, 153, 243, 0.3);
  }
}
</style> 