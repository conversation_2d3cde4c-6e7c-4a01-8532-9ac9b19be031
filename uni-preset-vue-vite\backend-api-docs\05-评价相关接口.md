# 评价相关接口

## 基础信息
- **基础URL**: `https://api.dentalcare.com/v1`
- **认证方式**: JWT <PERSON> (Bearer Authentication)
- **请求头**: `Authorization: Bearer {token}`

## 1. 评价提交

### 1.1 提交医生评价
- **URL**: `/ratings`
- **方法**: `POST`
- **描述**: 患者对医生进行评价
- **请求参数**:
```json
{
  "appointmentId": "apt001",
  "doctorId": "doc001",
  "rating": 5,
  "comment": "医生很专业，服务态度很好，治疗效果满意",
  "tags": ["专业", "耐心", "技术好"],
  "dimensions": {
    "professionalism": 5,
    "communication": 5,
    "environment": 4,
    "effectiveness": 5,
    "service": 5
  },
  "anonymous": false
}
```

- **成功响应**:
```json
{
  "code": 200,
  "message": "评价提交成功",
  "data": {
    "ratingId": "rat001",
    "appointmentId": "apt001",
    "doctorId": "doc001",
    "rating": 5,
    "comment": "医生很专业，服务态度很好，治疗效果满意",
    "tags": ["专业", "耐心", "技术好"],
    "anonymous": false,
    "createdAt": "2023-11-16T10:00:00Z",
    "status": "published"
  }
}
```

### 1.2 检查评价资格
- **URL**: `/ratings/check-eligibility`
- **方法**: `POST`
- **描述**: 检查患者是否可以对指定预约进行评价
- **请求参数**:
```json
{
  "appointmentId": "apt001"
}
```

- **成功响应**:
```json
{
  "code": 200,
  "message": "可以评价",
  "data": {
    "eligible": true,
    "appointmentInfo": {
      "appointmentId": "apt001",
      "doctorName": "李医生",
      "appointmentTime": "2023-11-15T14:00:00Z",
      "status": "completed"
    },
    "alreadyRated": false
  }
}
```

## 2. 评价查询

### 2.1 获取医生评价列表
- **URL**: `/doctors/{doctorId}/ratings`
- **方法**: `GET`
- **描述**: 获取指定医生的评价列表
- **查询参数**:
  - `rating`: 评分筛选（1-5）
  - `sortBy`: 排序方式（newest/oldest/rating_high/rating_low）
  - `page`: 页码
  - `limit`: 每页数量

- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "ratings": [
      {
        "ratingId": "rat001",
        "patientName": "张***",
        "rating": 5,
        "comment": "医生很专业，服务态度很好",
        "tags": ["专业", "耐心"],
        "dimensions": {
          "professionalism": 5,
          "communication": 5,
          "environment": 4,
          "effectiveness": 5,
          "service": 5
        },
        "appointmentDate": "2023-11-15T14:00:00Z",
        "createdAt": "2023-11-16T10:00:00Z",
        "helpful": 12,
        "replies": [
          {
            "replyId": "rep001",
            "content": "感谢您的评价，我们会继续努力",
            "repliedBy": "doctor",
            "repliedAt": "2023-11-16T15:00:00Z"
          }
        ]
      }
    ],
    "summary": {
      "averageRating": 4.8,
      "totalRatings": 156,
      "ratingDistribution": {
        "5": 120,
        "4": 25,
        "3": 8,
        "2": 2,
        "1": 1
      },
      "dimensionAverages": {
        "professionalism": 4.9,
        "communication": 4.7,
        "environment": 4.5,
        "effectiveness": 4.8,
        "service": 4.6
      },
      "popularTags": [
        {"tag": "专业", "count": 89},
        {"tag": "耐心", "count": 67},
        {"tag": "技术好", "count": 54}
      ]
    },
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 156,
      "totalPages": 16
    }
  }
}
```

### 2.2 获取患者评价历史
- **URL**: `/patients/{patientId}/ratings`
- **方法**: `GET`
- **描述**: 获取患者的评价历史
- **查询参数**:
  - `page`: 页码
  - `limit`: 每页数量

- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "ratings": [
      {
        "ratingId": "rat001",
        "doctorName": "李医生",
        "hospital": "北京口腔医院",
        "rating": 5,
        "comment": "医生很专业，服务态度很好",
        "appointmentDate": "2023-11-15T14:00:00Z",
        "createdAt": "2023-11-16T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 5,
      "totalPages": 1
    }
  }
}
```

### 2.3 获取评价详情
- **URL**: `/ratings/{ratingId}`
- **方法**: `GET`
- **描述**: 获取指定评价的详细信息
- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "ratingId": "rat001",
    "appointmentId": "apt001",
    "patientName": "张***",
    "doctorName": "李医生",
    "rating": 5,
    "comment": "医生很专业，服务态度很好，治疗效果满意",
    "tags": ["专业", "耐心", "技术好"],
    "dimensions": {
      "professionalism": 5,
      "communication": 5,
      "environment": 4,
      "effectiveness": 5,
      "service": 5
    },
    "appointmentDate": "2023-11-15T14:00:00Z",
    "createdAt": "2023-11-16T10:00:00Z",
    "helpful": 12,
    "anonymous": false,
    "status": "published"
  }
}
```

## 3. 评价互动

### 3.1 评价点赞
- **URL**: `/ratings/{ratingId}/helpful`
- **方法**: `POST`
- **描述**: 对评价点赞（认为有帮助）
- **成功响应**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "ratingId": "rat001",
    "helpful": 13,
    "userAction": "liked"
  }
}
```

### 3.2 取消点赞
- **URL**: `/ratings/{ratingId}/helpful`
- **方法**: `DELETE`
- **描述**: 取消对评价的点赞

### 3.3 医生回复评价
- **URL**: `/ratings/{ratingId}/reply`
- **方法**: `POST`
- **描述**: 医生回复患者评价
- **请求参数**:
```json
{
  "content": "感谢您的评价，我们会继续努力为患者提供更好的服务"
}
```

- **成功响应**:
```json
{
  "code": 200,
  "message": "回复成功",
  "data": {
    "replyId": "rep001",
    "ratingId": "rat001",
    "content": "感谢您的评价，我们会继续努力为患者提供更好的服务",
    "repliedBy": "doctor",
    "repliedAt": "2023-11-16T15:00:00Z"
  }
}
```

## 4. 评价管理

### 4.1 修改评价
- **URL**: `/ratings/{ratingId}`
- **方法**: `PUT`
- **描述**: 患者修改自己的评价（限制时间内）
- **请求参数**:
```json
{
  "rating": 4,
  "comment": "修改后的评价内容",
  "tags": ["专业", "耐心"],
  "dimensions": {
    "professionalism": 4,
    "communication": 4,
    "environment": 4,
    "effectiveness": 4,
    "service": 4
  }
}
```

### 4.2 删除评价
- **URL**: `/ratings/{ratingId}`
- **方法**: `DELETE`
- **描述**: 删除评价（仅限管理员或在特定条件下）

### 4.3 举报评价
- **URL**: `/ratings/{ratingId}/report`
- **方法**: `POST`
- **描述**: 举报不当评价
- **请求参数**:
```json
{
  "reason": "spam",
  "description": "评价内容不实，疑似恶意评价"
}
```

## 5. 评价统计

### 5.1 获取评价统计
- **URL**: `/ratings/statistics`
- **方法**: `GET`
- **描述**: 获取评价相关统计数据
- **查询参数**:
  - `doctorId`: 医生ID（可选）
  - `startDate`: 开始日期
  - `endDate`: 结束日期

- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "totalRatings": 1250,
    "averageRating": 4.6,
    "ratingTrend": [
      {"date": "2023-11-01", "average": 4.5, "count": 25},
      {"date": "2023-11-02", "average": 4.7, "count": 30}
    ],
    "topRatedDoctors": [
      {"doctorId": "doc001", "name": "李医生", "rating": 4.9, "count": 156},
      {"doctorId": "doc002", "name": "王医生", "rating": 4.8, "count": 142}
    ],
    "ratingDistribution": {
      "5": 650,
      "4": 400,
      "3": 150,
      "2": 35,
      "1": 15
    }
  }
}
```
