// 模拟口腔健康数据
const dentalRecords = [
  {
    id: 1,
    created_at: '2023-10-15 09:30:45',
    health_score: 88,
    caries_ratio: '5.50%',
    cavity_ratio: '3.20%',
    total_decay_ratio: '8.70%',
    plaque_ratio: '12.30%',
    cleanliness_score: 85,
    img: '/static/logo.png', // 使用项目中已有的图片
    processed_images: []
  },
  {
    id: 2,
    created_at: '2023-09-01 14:15:30',
    health_score: 82,
    caries_ratio: '7.20%',
    cavity_ratio: '4.50%',
    total_decay_ratio: '11.70%',
    plaque_ratio: '15.80%',
    cleanliness_score: 78,
    img: '/static/logo.png', // 使用项目中已有的图片
    processed_images: []
  },
  {
    id: 3,
    created_at: '2023-07-22 11:05:12',
    health_score: 90,
    caries_ratio: '4.30%',
    cavity_ratio: '2.80%',
    total_decay_ratio: '7.10%',
    plaque_ratio: '10.50%',
    cleanliness_score: 88,
    img: '/static/logo.png', // 使用项目中已有的图片
    processed_images: []
  }
]

// 模拟API接口
export default {
  // 获取所有记录
  getDentalRecords: () => {
    return {
      success: true,
      records: dentalRecords
    }
  },
  
  // 获取最新一条记录
  getLatestDentalRecord: () => {
    return {
      success: true,
      record: dentalRecords[0]
    }
  }
} 