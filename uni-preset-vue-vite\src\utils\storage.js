/**
 * 存储工具类，用于管理用户信息等状态
 */

// 存储键名
const KEYS = {
  USER_INFO: 'USER_INFO',
  TOKEN: 'DENTAL_HEALTH_TOKEN',
  TOKEN_EXPIRE: 'TOKEN_EXPIRE_TIME',
  USER_TYPE: 'USER_TYPE'
};

/**
 * 保存用户信息
 * @param {Object} userInfo - 用户信息对象
 */
export const saveUserInfo = (userInfo) => {
  if (userInfo) {
    uni.setStorageSync(KEYS.USER_INFO, JSON.stringify(userInfo));
    // 保存用户类型便于快速判断
    if (userInfo.userType) {
      uni.setStorageSync(KEYS.USER_TYPE, userInfo.userType);
    }
  }
};

/**
 * 获取用户信息
 * @returns {Object|null} - 用户信息对象或null
 */
export const getUserInfo = () => {
  const userInfoStr = uni.getStorageSync(KEYS.USER_INFO);
  if (userInfoStr) {
    try {
      return JSON.parse(userInfoStr);
    } catch (e) {
      console.error('解析用户信息失败', e);
      return null;
    }
  }
  return null;
};

/**
 * 获取用户ID
 * @returns {string|null} - 用户ID或null
 */
export const getUserId = () => {
  const userInfo = getUserInfo();
  return userInfo ? userInfo.userId : null;
};

/**
 * 获取用户类型
 * @returns {string|null} - 用户类型（patient/doctor/admin）或null
 */
export const getUserType = () => {
  return uni.getStorageSync(KEYS.USER_TYPE) || null;
};

/**
 * 判断用户是否为患者
 * @returns {boolean} - 是否为患者
 */
export const isPatient = () => {
  return getUserType() === 'patient';
};

/**
 * 判断用户是否为医生
 * @returns {boolean} - 是否为医生
 */
export const isDoctor = () => {
  return getUserType() === 'doctor';
};

/**
 * 判断用户是否为管理员
 * @returns {boolean} - 是否为管理员
 */
export const isAdmin = () => {
  return getUserType() === 'admin';
};

/**
 * 判断用户是否已登录
 * @returns {boolean} - 是否已登录
 */
export const isLoggedIn = () => {
  const token = uni.getStorageSync(KEYS.TOKEN);
  const userInfo = getUserInfo();
  return !!token && !!userInfo;
};

/**
 * 清除所有用户相关信息（登出）
 */
export const clearUserInfo = () => {
  uni.removeStorageSync(KEYS.USER_INFO);
  uni.removeStorageSync(KEYS.TOKEN);
  uni.removeStorageSync(KEYS.TOKEN_EXPIRE);
  uni.removeStorageSync(KEYS.USER_TYPE);
};

/**
 * 检查Token是否已过期
 * @returns {boolean} - 是否已过期
 */
export const isTokenExpired = () => {
  const expireTime = uni.getStorageSync(KEYS.TOKEN_EXPIRE);
  if (!expireTime) return true;
  
  // 提前5分钟判断过期，避免边界问题
  const currentTime = new Date().getTime();
  const bufferTime = 5 * 60 * 1000; // 5分钟的缓冲时间
  
  return currentTime + bufferTime > expireTime;
};

export default {
  saveUserInfo,
  getUserInfo,
  getUserId,
  getUserType,
  isPatient,
  isDoctor,
  isAdmin,
  isLoggedIn,
  clearUserInfo,
  isTokenExpired
}; 