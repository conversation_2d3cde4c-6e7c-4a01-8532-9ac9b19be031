# 数据库设计文档

## 数据库选型建议

### 推荐数据库
- **主数据库**: MySQL 8.0+ 或 PostgreSQL 13+
- **缓存数据库**: Redis 6.0+
- **文件存储**: 阿里云OSS / 腾讯云COS / AWS S3

### 数据库特性要求
- 支持事务处理（ACID）
- 支持JSON数据类型
- 支持全文搜索
- 支持分区表
- 支持读写分离

## 核心数据表设计

### 1. 用户表 (users)
```sql
CREATE TABLE users (
  id VARCHAR(36) PRIMARY KEY COMMENT '用户ID（UUID）',
  user_type ENUM('patient', 'doctor', 'admin') NOT NULL COMMENT '用户类型',
  phone VARCHAR(20) UNIQUE NOT NULL COMMENT '手机号',
  email VARCHAR(100) UNIQUE COMMENT '邮箱',
  password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
  name VARCHAR(50) NOT NULL COMMENT '姓名',
  gender ENUM('男', '女', '未知') DEFAULT '未知' COMMENT '性别',
  avatar VARCHAR(500) COMMENT '头像URL',
  status ENUM('active', 'inactive', 'banned') DEFAULT 'active' COMMENT '账户状态',
  last_login_at TIMESTAMP COMMENT '最后登录时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_phone (phone),
  INDEX idx_email (email),
  INDEX idx_user_type (user_type),
  INDEX idx_status (status)
) COMMENT '用户基础表';
```

### 2. 患者信息表 (patients)
```sql
CREATE TABLE patients (
  id VARCHAR(36) PRIMARY KEY COMMENT '患者ID',
  user_id VARCHAR(36) UNIQUE NOT NULL COMMENT '关联用户ID',
  age INT COMMENT '年龄',
  address VARCHAR(200) COMMENT '地址',
  emergency_contact_name VARCHAR(50) COMMENT '紧急联系人姓名',
  emergency_contact_phone VARCHAR(20) COMMENT '紧急联系人电话',
  emergency_contact_relationship VARCHAR(20) COMMENT '紧急联系人关系',
  medical_history TEXT COMMENT '病史',
  allergies TEXT COMMENT '过敏史',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_id (user_id)
) COMMENT '患者详细信息表';
```

### 3. 医生信息表 (doctors)
```sql
CREATE TABLE doctors (
  id VARCHAR(36) PRIMARY KEY COMMENT '医生ID',
  user_id VARCHAR(36) UNIQUE NOT NULL COMMENT '关联用户ID',
  specialty VARCHAR(50) NOT NULL COMMENT '专科',
  hospital VARCHAR(100) NOT NULL COMMENT '医院',
  department VARCHAR(50) COMMENT '科室',
  title VARCHAR(50) COMMENT '职称',
  experience INT COMMENT '从业年限',
  license_number VARCHAR(50) UNIQUE COMMENT '执业证书号',
  introduction TEXT COMMENT '个人介绍',
  consultation_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '咨询费用',
  rating DECIMAL(3,2) DEFAULT 0.00 COMMENT '平均评分',
  rating_count INT DEFAULT 0 COMMENT '评价数量',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_id (user_id),
  INDEX idx_specialty (specialty),
  INDEX idx_hospital (hospital),
  INDEX idx_rating (rating)
) COMMENT '医生详细信息表';
```

### 4. 医生可预约时间表 (doctor_available_times)
```sql
CREATE TABLE doctor_available_times (
  id VARCHAR(36) PRIMARY KEY COMMENT '时间段ID',
  doctor_id VARCHAR(36) NOT NULL COMMENT '医生ID',
  date DATE NOT NULL COMMENT '日期',
  start_time TIME NOT NULL COMMENT '开始时间',
  end_time TIME NOT NULL COMMENT '结束时间',
  appointment_type ENUM('consultation', 'treatment', 'followup') DEFAULT 'consultation' COMMENT '预约类型',
  max_patients INT DEFAULT 1 COMMENT '最大患者数',
  booked_patients INT DEFAULT 0 COMMENT '已预约患者数',
  status ENUM('available', 'booked', 'cancelled') DEFAULT 'available' COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (doctor_id) REFERENCES doctors(id) ON DELETE CASCADE,
  UNIQUE KEY uk_doctor_datetime (doctor_id, date, start_time),
  INDEX idx_doctor_id (doctor_id),
  INDEX idx_date (date),
  INDEX idx_status (status)
) COMMENT '医生可预约时间表';
```

### 5. 预约表 (appointments)
```sql
CREATE TABLE appointments (
  id VARCHAR(36) PRIMARY KEY COMMENT '预约ID',
  appointment_number VARCHAR(20) UNIQUE NOT NULL COMMENT '预约号',
  patient_id VARCHAR(36) NOT NULL COMMENT '患者ID',
  doctor_id VARCHAR(36) NOT NULL COMMENT '医生ID',
  time_slot_id VARCHAR(36) COMMENT '时间段ID',
  appointment_time TIMESTAMP NOT NULL COMMENT '预约时间',
  appointment_type ENUM('consultation', 'treatment', 'followup') DEFAULT 'consultation' COMMENT '预约类型',
  status ENUM('pending', 'confirmed', 'completed', 'cancelled') DEFAULT 'pending' COMMENT '预约状态',
  symptoms TEXT COMMENT '症状描述',
  notes TEXT COMMENT '备注',
  doctor_notes TEXT COMMENT '医生备注',
  estimated_duration INT DEFAULT 30 COMMENT '预计时长（分钟）',
  actual_duration INT COMMENT '实际时长（分钟）',
  contact_phone VARCHAR(20) COMMENT '联系电话',
  cancel_reason VARCHAR(200) COMMENT '取消原因',
  cancelled_by ENUM('patient', 'doctor', 'admin') COMMENT '取消方',
  cancelled_at TIMESTAMP COMMENT '取消时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
  FOREIGN KEY (doctor_id) REFERENCES doctors(id) ON DELETE CASCADE,
  FOREIGN KEY (time_slot_id) REFERENCES doctor_available_times(id),
  INDEX idx_patient_id (patient_id),
  INDEX idx_doctor_id (doctor_id),
  INDEX idx_appointment_time (appointment_time),
  INDEX idx_status (status),
  INDEX idx_appointment_number (appointment_number)
) COMMENT '预约表';
```

### 6. 评价表 (ratings)
```sql
CREATE TABLE ratings (
  id VARCHAR(36) PRIMARY KEY COMMENT '评价ID',
  appointment_id VARCHAR(36) UNIQUE NOT NULL COMMENT '预约ID',
  patient_id VARCHAR(36) NOT NULL COMMENT '患者ID',
  doctor_id VARCHAR(36) NOT NULL COMMENT '医生ID',
  rating TINYINT NOT NULL COMMENT '评分（1-5）',
  comment TEXT COMMENT '评价内容',
  tags JSON COMMENT '评价标签',
  dimensions JSON COMMENT '各维度评分',
  anonymous BOOLEAN DEFAULT FALSE COMMENT '是否匿名',
  helpful_count INT DEFAULT 0 COMMENT '有用数',
  status ENUM('published', 'hidden', 'reported') DEFAULT 'published' COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (appointment_id) REFERENCES appointments(id) ON DELETE CASCADE,
  FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
  FOREIGN KEY (doctor_id) REFERENCES doctors(id) ON DELETE CASCADE,
  INDEX idx_doctor_id (doctor_id),
  INDEX idx_patient_id (patient_id),
  INDEX idx_rating (rating),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
) COMMENT '评价表';
```

### 7. 评价回复表 (rating_replies)
```sql
CREATE TABLE rating_replies (
  id VARCHAR(36) PRIMARY KEY COMMENT '回复ID',
  rating_id VARCHAR(36) NOT NULL COMMENT '评价ID',
  user_id VARCHAR(36) NOT NULL COMMENT '回复用户ID',
  user_type ENUM('doctor', 'admin') NOT NULL COMMENT '回复用户类型',
  content TEXT NOT NULL COMMENT '回复内容',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (rating_id) REFERENCES ratings(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_rating_id (rating_id),
  INDEX idx_user_id (user_id)
) COMMENT '评价回复表';
```

### 8. 通知表 (notifications)
```sql
CREATE TABLE notifications (
  id VARCHAR(36) PRIMARY KEY COMMENT '通知ID',
  user_id VARCHAR(36) NOT NULL COMMENT '用户ID',
  type ENUM('appointment', 'system', 'rating', 'reminder') NOT NULL COMMENT '通知类型',
  title VARCHAR(100) NOT NULL COMMENT '通知标题',
  content TEXT NOT NULL COMMENT '通知内容',
  status ENUM('unread', 'read') DEFAULT 'unread' COMMENT '状态',
  priority ENUM('low', 'medium', 'high') DEFAULT 'medium' COMMENT '优先级',
  related_id VARCHAR(36) COMMENT '关联ID',
  related_type VARCHAR(50) COMMENT '关联类型',
  action_url VARCHAR(500) COMMENT '操作链接',
  metadata JSON COMMENT '元数据',
  read_at TIMESTAMP COMMENT '阅读时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_id (user_id),
  INDEX idx_type (type),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
) COMMENT '通知表';
```

### 9. 患者健康数据表 (patient_health_data)
```sql
CREATE TABLE patient_health_data (
  id VARCHAR(36) PRIMARY KEY COMMENT '健康数据ID',
  patient_id VARCHAR(36) NOT NULL COMMENT '患者ID',
  health_score INT COMMENT '健康评分',
  cavity_rate VARCHAR(10) COMMENT '龋齿率',
  periodontal_rate VARCHAR(10) COMMENT '牙周病率',
  plaque_rate VARCHAR(10) COMMENT '牙菌斑率',
  cleanliness_score INT COMMENT '清洁度评分',
  risk_level ENUM('low', 'medium', 'high') COMMENT '风险等级',
  recommendations JSON COMMENT '健康建议',
  check_date DATE COMMENT '检查日期',
  notes TEXT COMMENT '备注',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
  INDEX idx_patient_id (patient_id),
  INDEX idx_check_date (check_date)
) COMMENT '患者健康数据表';
```

### 10. 文件表 (files)
```sql
CREATE TABLE files (
  id VARCHAR(36) PRIMARY KEY COMMENT '文件ID',
  user_id VARCHAR(36) NOT NULL COMMENT '上传用户ID',
  file_name VARCHAR(255) NOT NULL COMMENT '文件名',
  original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
  file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
  file_size BIGINT NOT NULL COMMENT '文件大小（字节）',
  mime_type VARCHAR(100) NOT NULL COMMENT 'MIME类型',
  file_type ENUM('avatar', 'symptom', 'document', 'other') NOT NULL COMMENT '文件类型',
  url VARCHAR(500) NOT NULL COMMENT '访问URL',
  thumbnail_url VARCHAR(500) COMMENT '缩略图URL',
  metadata JSON COMMENT '文件元数据',
  status ENUM('active', 'deleted') DEFAULT 'active' COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_id (user_id),
  INDEX idx_file_type (file_type),
  INDEX idx_status (status)
) COMMENT '文件表';

## 索引设计建议

### 主要索引
1. **用户表索引**
   - 主键索引：id
   - 唯一索引：phone, email
   - 复合索引：(user_type, status)

2. **预约表索引**
   - 主键索引：id
   - 唯一索引：appointment_number
   - 复合索引：(patient_id, status), (doctor_id, appointment_time)

3. **评价表索引**
   - 主键索引：id
   - 复合索引：(doctor_id, rating), (created_at, status)

### 性能优化建议
1. **分区策略**
   - 预约表按月分区
   - 通知表按季度分区
   - 评价表按年分区

2. **缓存策略**
   - 医生信息缓存（Redis）
   - 热门医生列表缓存
   - 用户会话缓存

3. **读写分离**
   - 主库：写操作
   - 从库：读操作、报表查询

## 数据库关系图

```
users (用户基础表)
├── patients (患者信息)
├── doctors (医生信息)
└── notifications (通知)

doctors (医生)
├── doctor_available_times (可预约时间)
├── appointments (预约记录)
└── ratings (评价)

appointments (预约)
├── ratings (评价)
└── files (相关文件)

patients (患者)
├── appointments (预约记录)
├── patient_health_data (健康数据)
└── ratings (评价)
```
```
