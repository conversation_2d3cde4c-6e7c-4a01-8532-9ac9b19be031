# 管理员相关接口

## 基础信息
- **基础URL**: `https://api.dentalcare.com/v1`
- **认证方式**: JWT <PERSON> (Bearer Authentication)
- **请求头**: `Authorization: Bearer {token}`
- **权限要求**: 管理员角色

## 1. 用户管理

### 1.1 获取患者列表
- **URL**: `/admin/patients`
- **方法**: `GET`
- **描述**: 获取所有患者列表
- **查询参数**:
  - `search`: 搜索关键词（姓名、手机号）
  - `status`: 账户状态（active/inactive/banned）
  - `registrationDate`: 注册日期范围
  - `page`: 页码
  - `limit`: 每页数量

- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "patients": [
      {
        "patientId": "pat001",
        "name": "张三",
        "phone": "13800138000",
        "email": "<EMAIL>",
        "gender": "男",
        "age": 28,
        "status": "active",
        "registrationDate": "2023-01-15T08:30:00Z",
        "lastLoginTime": "2023-11-15T10:20:00Z",
        "appointmentCount": 5,
        "totalSpent": 2500.00
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 1250,
      "totalPages": 63
    }
  }
}
```

### 1.2 获取医生列表
- **URL**: `/admin/doctors`
- **方法**: `GET`
- **描述**: 获取所有医生列表
- **查询参数**:
  - `search`: 搜索关键词（姓名、手机号）
  - `specialty`: 专科筛选
  - `hospital`: 医院筛选
  - `status`: 账户状态
  - `page`: 页码
  - `limit`: 每页数量

- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "doctors": [
      {
        "doctorId": "doc001",
        "name": "李医生",
        "phone": "13900139000",
        "email": "<EMAIL>",
        "specialty": "口腔正畸",
        "hospital": "北京口腔医院",
        "title": "主治医师",
        "status": "active",
        "rating": 4.8,
        "appointmentCount": 156,
        "registrationDate": "2023-01-10T08:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 85,
      "totalPages": 5
    }
  }
}
```

### 1.3 添加医生账号
- **URL**: `/admin/doctors`
- **方法**: `POST`
- **描述**: 管理员添加新医生账号
- **请求参数**:
```json
{
  "name": "王医生",
  "phone": "13700137000",
  "email": "<EMAIL>",
  "password": "temp123456",
  "specialty": "口腔修复",
  "hospital": "北京口腔医院",
  "department": "修复科",
  "title": "副主任医师",
  "experience": 8,
  "licenseNumber": "110101198501011234",
  "introduction": "专业从事口腔修复治疗"
}
```

### 1.4 更新用户状态
- **URL**: `/admin/users/{userId}/status`
- **方法**: `PUT`
- **描述**: 更新用户账户状态
- **请求参数**:
```json
{
  "status": "banned",
  "reason": "违反平台规定",
  "notes": "多次恶意评价"
}
```

### 1.5 删除用户账号
- **URL**: `/admin/users/{userId}`
- **方法**: `DELETE`
- **描述**: 删除用户账号
- **请求参数**:
```json
{
  "reason": "用户申请注销",
  "dataRetention": false
}
```

## 2. 预约管理

### 2.1 获取所有预约列表
- **URL**: `/admin/appointments`
- **方法**: `GET`
- **描述**: 获取系统中所有预约记录
- **查询参数**:
  - `status`: 预约状态
  - `doctorId`: 医生ID
  - `patientId`: 患者ID
  - `hospital`: 医院筛选
  - `startDate`: 开始日期
  - `endDate`: 结束日期
  - `page`: 页码
  - `limit`: 每页数量

### 2.2 强制取消预约
- **URL**: `/admin/appointments/{appointmentId}/force-cancel`
- **方法**: `PUT`
- **描述**: 管理员强制取消预约
- **请求参数**:
```json
{
  "reason": "医生临时请假",
  "notifyPatient": true,
  "notifyDoctor": true,
  "compensationOffered": true
}
```

## 3. 评价管理

### 3.1 获取所有评价列表
- **URL**: `/admin/ratings`
- **方法**: `GET`
- **描述**: 获取系统中所有评价
- **查询参数**:
  - `status`: 评价状态（published/hidden/reported）
  - `rating`: 评分筛选
  - `doctorId`: 医生ID
  - `reported`: 是否被举报
  - `page`: 页码
  - `limit`: 每页数量

### 3.2 处理举报评价
- **URL**: `/admin/ratings/{ratingId}/handle-report`
- **方法**: `PUT`
- **描述**: 处理被举报的评价
- **请求参数**:
```json
{
  "action": "hide",
  "reason": "评价内容不实",
  "notes": "经核实，该评价存在恶意诋毁"
}
```

### 3.3 删除评价
- **URL**: `/admin/ratings/{ratingId}`
- **方法**: `DELETE`
- **描述**: 管理员删除评价

## 4. 系统统计

### 4.1 获取系统概览统计
- **URL**: `/admin/statistics/overview`
- **方法**: `GET`
- **描述**: 获取系统整体统计数据
- **成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "userStatistics": {
      "totalPatients": 1250,
      "totalDoctors": 85,
      "activePatients": 1180,
      "activeDoctors": 78,
      "newPatientsThisMonth": 45,
      "newDoctorsThisMonth": 3
    },
    "appointmentStatistics": {
      "totalAppointments": 5680,
      "completedAppointments": 4520,
      "cancelledAppointments": 680,
      "pendingAppointments": 480,
      "appointmentsToday": 25,
      "appointmentsThisWeek": 156
    },
    "ratingStatistics": {
      "totalRatings": 3250,
      "averageRating": 4.6,
      "ratingsThisMonth": 180,
      "reportedRatings": 12
    },
    "revenueStatistics": {
      "totalRevenue": 1250000.00,
      "revenueThisMonth": 85000.00,
      "averageOrderValue": 220.00
    }
  }
}
```

### 4.2 获取用户增长统计
- **URL**: `/admin/statistics/user-growth`
- **方法**: `GET`
- **描述**: 获取用户增长趋势数据
- **查询参数**:
  - `period`: 时间周期（daily/weekly/monthly）
  - `startDate`: 开始日期
  - `endDate`: 结束日期

### 4.3 获取预约统计
- **URL**: `/admin/statistics/appointments`
- **方法**: `GET`
- **描述**: 获取预约相关统计数据

### 4.4 获取医生绩效统计
- **URL**: `/admin/statistics/doctor-performance`
- **方法**: `GET`
- **描述**: 获取医生绩效统计
- **查询参数**:
  - `doctorId`: 医生ID（可选）
  - `startDate`: 开始日期
  - `endDate`: 结束日期

## 5. 系统配置

### 5.1 获取系统配置
- **URL**: `/admin/system/config`
- **方法**: `GET`
- **描述**: 获取系统配置信息

### 5.2 更新系统配置
- **URL**: `/admin/system/config`
- **方法**: `PUT`
- **描述**: 更新系统配置
- **请求参数**:
```json
{
  "appointmentSettings": {
    "maxAdvanceBookingDays": 30,
    "minAdvanceBookingHours": 2,
    "cancellationDeadlineHours": 24
  },
  "ratingSettings": {
    "enableAnonymousRating": true,
    "ratingEditTimeLimit": 24,
    "autoHideReportedRatings": false
  },
  "notificationSettings": {
    "enableSmsReminders": true,
    "enablePushNotifications": true,
    "reminderTimings": ["24_hours", "1_hour"]
  }
}
```

## 6. 日志管理

### 6.1 获取操作日志
- **URL**: `/admin/logs/operations`
- **方法**: `GET`
- **描述**: 获取系统操作日志
- **查询参数**:
  - `userId`: 用户ID
  - `action`: 操作类型
  - `startDate`: 开始日期
  - `endDate`: 结束日期
  - `page`: 页码
  - `limit`: 每页数量

### 6.2 获取错误日志
- **URL**: `/admin/logs/errors`
- **方法**: `GET`
- **描述**: 获取系统错误日志

## 状态码说明
- 200: 成功
- 400: 请求参数错误
- 401: 未授权
- 403: 权限不足（非管理员）
- 404: 资源不存在
- 500: 服务器内部错误
