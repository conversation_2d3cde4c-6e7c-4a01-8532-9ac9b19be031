# 口腔健康预约系统API接口文档

## 基础信息

- **基础URL**: `https://locokdvuripk.sealosbja.site/api`
- **服务器端口**: 3001
- **数据格式**: JSON
- **认证方式**: JWT <PERSON> (Bearer Authentication)

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": { ... }  // 具体数据，可能为对象、数组或null
}
```

### 错误响应
```json
{
  "code": 400-500,  // 错误代码
  "message": "错误描述"
}
```

### 通用状态码
- 200: 成功
- 400: 请求参数错误
- 401: 未授权（未登录或token过期）
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器内部错误

## 1. 用户认证相关接口

### 1.1 用户注册

- **URL**: `/auth/register`
- **方法**: `POST`
- **描述**: 注册新用户（患者/医生）
- **请求参数**:
  ```json
  {
    "userType": "patient|doctor", // 用户类型：患者或医生
    "name": "张三",               // 用户姓名
    "gender": "男",               // 性别
    "phone": "***********",       // 手机号码
    "email": "<EMAIL>",  // 电子邮箱
    "password": "password123",    // 密码
    "securityQuestion": "我的出生地是？", // 安全问题
    "securityAnswer": "北京"       // 安全问题答案
  }
  ```
- **成功响应**:
  ```json
  {
    "code": 200,
    "message": "注册成功",
    "data": {
      "userId": "12345",
      "userType": "patient|doctor",
      "name": "张三"
    }
  }
  ```
- **错误响应**:
  ```json
  {
    "code": 400,
    "message": "手机号或邮箱已被注册"
  }
  ```
  或
  ```json
  {
    "code": 400,
    "message": "无效的用户类型"
  }
  ```

### 1.2 用户登录

- **URL**: `/auth/login`
- **方法**: `POST`
- **描述**: 用户登录（患者/医生/管理员）
- **请求参数**:
  ```json
  {
    "account": "***********|<EMAIL>", // 手机号或邮箱
    "password": "password123",                 // 密码
    "userType": "patient|doctor|admin"         // 用户类型
  }
  ```
- **成功响应**:
  ```json
  {
    "code": 200,
    "message": "登录成功",
    "data": {
      "userId": "12345",
      "userType": "patient|doctor|admin",
      "name": "张三",
      "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", // JWT Token
      "expireTime": ************* // Token过期时间戳
    }
  }
  ```
- **错误响应**:
  ```json
  {
    "code": 401,
    "message": "账号或密码错误"
  }
  ```
  或
  ```json
  {
    "code": 400,
    "message": "无效的用户类型"
  }
  ```

### 1.3 找回密码

- **URL**: `/auth/forgot-password`
- **方法**: `POST`
- **描述**: 通过安全问题找回密码
- **请求参数**:
  ```json
  {
    "account": "***********|<EMAIL>", // 手机号或邮箱
    "securityQuestion": "我的出生地是？",      // 安全问题
    "securityAnswer": "北京"                   // 安全问题答案
  }
  ```
- **成功响应**:
  ```json
  {
    "code": 200,
    "message": "验证成功",
    "data": {
      "resetToken": "abc123def456" // 重置密码的临时令牌
    }
  }
  ```
- **错误响应**:
  ```json
  {
    "code": 404,
    "message": "用户不存在"
  }
  ```
  或
  ```json
  {
    "code": 401,
    "message": "安全问题或答案错误"
  }
  ```

### 1.4 重置密码

- **URL**: `/auth/reset-password`
- **方法**: `POST`
- **描述**: 重置用户密码
- **请求参数**:
  ```json
  {
    "resetToken": "abc123def456",    // 重置密码的临时令牌
    "newPassword": "newPassword123"  // 新密码
  }
  ```
- **成功响应**:
  ```json
  {
    "code": 200,
    "message": "密码重置成功"
  }
  ```
- **错误响应**:
  ```json
  {
    "code": 400,
    "message": "无效的重置令牌"
  }
  ```

### 1.5 退出登录

- **URL**: `/auth/logout`
- **方法**: `POST`
- **描述**: 用户退出登录
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **成功响应**:
  ```json
  {
    "code": 200,
    "message": "退出成功"
  }
  ```
- **错误响应**:
  ```json
  {
    "code": 401,
    "message": "未提供认证令牌"
  }
  ```

## 2. 患者相关接口

### 2.1 获取患者个人信息

- **URL**: `/patients/{patientId}`
- **方法**: `GET`
- **描述**: 获取患者个人信息
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **成功响应**:
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "id": "P10001",
      "name": "张三",
      "gender": "男",
      "phone": "***********",
      "email": "<EMAIL>",
      "location": {
        "latitude": 39.9042,
        "longitude": 116.4074,
        "address": "北京市朝阳区"
      }
    }
  }
  ```
- **错误响应**:
  ```json
  {
    "code": 404,
    "message": "患者不存在"
  }
  ```
  或
  ```json
  {
    "code": 403,
    "message": "权限不足，您无法访问此资源"
  }
  ```

### 2.2 更新患者个人信息

- **URL**: `/patients/{patientId}`
- **方法**: `PUT`
- **描述**: 更新患者个人信息
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **请求参数**:
  ```json
  {
    "name": "张三",
    "gender": "男",
    "phone": "***********",
    "email": "<EMAIL>",
    "location": {
      "latitude": 39.9042,
      "longitude": 116.4074,
      "address": "北京市朝阳区"
    }
  }
  ```
- **成功响应**:
  ```json
  {
    "code": 200,
    "message": "更新成功"
  }
  ```
- **错误响应**:
  ```json
  {
    "code": 404,
    "message": "患者不存在"
  }
  ```
  或
  ```json
  {
    "code": 400,
    "message": "手机号或邮箱已被其他用户使用"
  }
  ```

### 2.3 获取推荐医生列表

- **URL**: `/patients/recommended-doctors`
- **方法**: `GET`
- **描述**: 基于患者位置和医生专科智能推荐医生
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **请求参数**:
  ```
  latitude: 39.9042         // 患者当前纬度
  longitude: 116.4074       // 患者当前经度
  specialization: 口腔正畸科 // 可选，筛选专科
  maxDistance: 10           // 可选，最大距离（公里）
  pageSize: 10              // 每页数量
  pageNum: 1                // 页码
  ```
- **成功响应**:
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "total": 25,
      "list": [
        {
          "id": "D10001",
          "name": "李医生",
          "gender": "男",
          "specialization": "口腔正畸科",
          "clinicLocation": {
            "latitude": 39.9142,
            "longitude": 116.4174,
            "address": "北京市朝阳区健康路88号"
          },
          "distance": 1.5, // 公里
          "introduction": "从事口腔医学工作10年，擅长各类牙齿矫正和美容修复",
          "rating": 4.8,   // 评分
          "ratingCount": 56 // 评分数量
        }
      ]
    }
  }
  ```
- **错误响应**:
  ```json
  {
    "code": 400,
    "message": "缺少位置参数"
  }
  ```

### 2.4 获取患者预约列表

- **URL**: `/patients/{patientId}/appointments`
- **方法**: `GET`
- **描述**: 获取患者的预约列表
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **请求参数**:
  ```
  status: upcoming|past|all  // 预约状态：即将到来/已过期/全部
  pageSize: 10               // 每页数量
  pageNum: 1                 // 页码
  ```
- **成功响应**:
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "total": 5,
      "list": [
        {
          "id": "A10001",
          "date": "2023-11-15",
          "startTime": "09:00",
          "endTime": "10:00",
          "status": "upcoming", // upcoming, completed, cancelled
          "doctor": {
            "id": "D10001",
            "name": "李医生",
            "specialization": "口腔正畸科",
            "clinicAddress": "北京市朝阳区健康路88号"
          },
          "description": "牙齿矫正咨询",
          "createTime": "2023-11-01 10:00:00"
        }
      ]
    }
  }
  ```
- **错误响应**:
  ```json
  {
    "code": 404,
    "message": "患者不存在"
  }
  ```

## 3. 医生相关接口

### 3.1 获取医生个人信息

- **URL**: `/doctors/{doctorId}`
- **方法**: `GET`
- **描述**: 获取医生个人信息
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **成功响应**:
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "id": "D10001",
      "name": "李医生",
      "gender": "男",
      "phone": "***********",
      "email": "<EMAIL>",
      "clinicLocation": {
        "latitude": 39.9142,
        "longitude": 116.4174,
        "address": "北京市朝阳区健康路88号"
      },
      "specialization": "口腔正畸科",
      "introduction": "从事口腔医学工作10年，擅长各类牙齿矫正和美容修复"
    }
  }
  ```
- **错误响应**:
  ```json
  {
    "code": 404,
    "message": "医生不存在"
  }
  ```

### 3.2 更新医生个人信息

- **URL**: `/doctors/{doctorId}`
- **方法**: `PUT`
- **描述**: 更新医生个人信息
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **请求参数**:
  ```json
  {
    "name": "李医生",
    "gender": "男",
    "phone": "***********",
    "email": "<EMAIL>",
    "clinicLocation": {
      "latitude": 39.9142,
      "longitude": 116.4174,
      "address": "北京市朝阳区健康路88号"
    },
    "specialization": "口腔正畸科",
    "introduction": "从事口腔医学工作10年，擅长各类牙齿矫正和美容修复"
  }
  ```
- **成功响应**:
  ```json
  {
    "code": 200,
    "message": "更新成功"
  }
  ```
- **错误响应**:
  ```json
  {
    "code": 404,
    "message": "医生不存在"
  }
  ```
  或
  ```json
  {
    "code": 400,
    "message": "手机号或邮箱已被其他用户使用"
  }
  ```

### 3.3 获取医生空闲时间列表

- **URL**: `/doctors/{doctorId}/available-times`
- **方法**: `GET`
- **描述**: 获取医生设置的空闲时间列表
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **请求参数**:
  ```
  startDate: 2023-11-01  // 开始日期
  endDate: 2023-11-30    // 结束日期
  ```
- **成功响应**:
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "availableTimes": [
        {
          "id": "AT10001",
          "date": "2023-11-15",
          "startTime": "09:00",
          "endTime": "12:00",
          "isBooked": false
        },
        {
          "id": "AT10002",
          "date": "2023-11-15",
          "startTime": "14:00",
          "endTime": "17:00",
          "isBooked": true
        }
      ]
    }
  }
  ```
- **错误响应**:
  ```json
  {
    "code": 404,
    "message": "医生不存在"
  }
  ```
  或
  ```json
  {
    "code": 400,
    "message": "缺少日期参数"
  }
  ```

### 3.4 添加医生空闲时间

- **URL**: `/doctors/{doctorId}/available-times`
- **方法**: `POST`
- **描述**: 医生添加空闲时间
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **请求参数**:
  ```json
  {
    "date": "2023-11-15",
    "startTime": "09:00",
    "endTime": "12:00"
  }
  ```
- **成功响应**:
  ```json
  {
    "code": 200,
    "message": "添加成功",
    "data": {
      "id": "AT10001",
      "date": "2023-11-15",
      "startTime": "09:00",
      "endTime": "12:00",
      "isBooked": false
    }
  }
  ```
- **错误响应**:
  ```json
  {
    "code": 400,
    "message": "时间段冲突，请选择其他时间"
  }
  ```
  或
  ```json
  {
    "code": 400,
    "message": "开始时间必须早于结束时间"
  }
  ```

### 3.5 删除医生空闲时间

- **URL**: `/doctors/{doctorId}/available-times/{timeId}`
- **方法**: `DELETE`
- **描述**: 医生删除空闲时间（未被预约的）
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **成功响应**:
  ```json
  {
    "code": 200,
    "message": "删除成功"
  }
  ```
- **错误响应**:
  ```json
  {
    "code": 404,
    "message": "空闲时间不存在"
  }
  ```
  或
  ```json
  {
    "code": 400,
    "message": "该时间段已被预约，无法删除"
  }
  ```

### 3.6 获取医生预约列表

- **URL**: `/doctors/{doctorId}/appointments`
- **方法**: `GET`
- **描述**: 获取医生的预约列表
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **请求参数**:
  ```
  date: 2023-11-15   // 可选，按日期筛选
  status: upcoming|past|all  // 预约状态：即将到来/已过期/全部
  pageSize: 10               // 每页数量
  pageNum: 1                 // 页码
  ```
- **成功响应**:
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "total": 8,
      "list": [
        {
          "id": "A10001",
          "date": "2023-11-15",
          "startTime": "09:00",
          "endTime": "10:00",
          "status": "upcoming", // upcoming, completed, cancelled
          "patient": {
            "id": "P10001",
            "name": "张三",
            "gender": "男",
            "phone": "***********"
          },
          "description": "牙齿矫正咨询",
          "createTime": "2023-11-01 10:00:00"
        }
      ]
    }
  }
  ```
- **错误响应**:
  ```json
  {
    "code": 404,
    "message": "医生不存在"
  }
  ```

### 3.7 获取医生评分列表

- **URL**: `/doctors/{doctorId}/ratings`
- **方法**: `GET`
- **描述**: 获取医生的评分列表
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **请求参数**:
  ```
  pageSize: 10      // 每页数量
  pageNum: 1        // 页码
  ```
- **成功响应**:
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "averageScore": 4.8,
      "totalRatings": 56,
      "list": [
        {
          "id": "R10001",
          "patientName": "张**", // 隐私保护
          "score": 5,
          "comment": "医生很专业，服务态度也很好",
          "createTime": "2023-11-01 15:30:00"
        }
      ]
    }
  }
  ```
- **错误响应**:
  ```json
  {
    "code": 404,
    "message": "医生不存在"
  }
  ```

## 4. 管理员相关接口

### 4.1 获取所有患者列表

- **URL**: `/admin/patients`
- **方法**: `GET`
- **描述**: 管理员获取所有患者列表
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **请求参数**:
  ```
  keyword: 张三     // 可选，搜索关键词（姓名/手机号）
  pageSize: 10      // 每页数量
  pageNum: 1        // 页码
  ```
- **成功响应**:
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "total": 100,
      "list": [
        {
          "id": "P10001",
          "name": "张三",
          "gender": "男",
          "phone": "***********",
          "email": "<EMAIL>",
          "registerTime": "2023-10-01 10:00:00"
        }
      ]
    }
  }
  ```
- **错误响应**:
  ```json
  {
    "code": 403,
    "message": "权限不足，需要管理员权限"
  }
  ```

### 4.2 获取所有医生列表

- **URL**: `/admin/doctors`
- **方法**: `GET`
- **描述**: 管理员获取所有医生列表
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **请求参数**:
  ```
  keyword: 李医生   // 可选，搜索关键词（姓名/手机号/专科）
  pageSize: 10      // 每页数量
  pageNum: 1        // 页码
  ```
- **成功响应**:
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "total": 50,
      "list": [
        {
          "id": "D10001",
          "name": "李医生",
          "gender": "男",
          "phone": "***********",
          "email": "<EMAIL>",
          "specialization": "口腔正畸科",
          "clinicLocation": "北京市朝阳区健康路88号",
          "registerTime": "2023-10-01 10:00:00"
        }
      ]
    }
  }
  ```
- **错误响应**:
  ```json
  {
    "code": 403,
    "message": "权限不足，需要管理员权限"
  }
  ```

### 4.3 添加医生账号

- **URL**: `/admin/doctors`
- **方法**: `POST`
- **描述**: 管理员添加医生账号
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **请求参数**:
  ```json
  {
    "name": "李医生",
    "gender": "男",
    "phone": "***********",
    "email": "<EMAIL>",
    "password": "password123",
    "clinicLocation": {
      "latitude": 39.9142,
      "longitude": 116.4174,
      "address": "北京市朝阳区健康路88号"
    },
    "specialization": "口腔正畸科",
    "introduction": "从事口腔医学工作10年，擅长各类牙齿矫正和美容修复"
  }
  ```
- **成功响应**:
  ```json
  {
    "code": 200,
    "message": "添加成功",
    "data": {
      "id": "D10001"
    }
  }
  ```
- **错误响应**:
  ```json
  {
    "code": 400,
    "message": "手机号或邮箱已被注册"
  }
  ```
  或
  ```json
  {
    "code": 403,
    "message": "权限不足，需要管理员权限"
  }
  ```

### 4.4 修改用户账号信息

- **URL**: `/admin/users/{userId}`
- **方法**: `PUT`
- **描述**: 管理员修改用户（患者/医生）账号信息
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **请求参数**:
  ```json
  {
    "name": "张三",
    "gender": "男",
    "phone": "***********",
    "email": "<EMAIL>",
    "password": "newPassword123" // 可选，如果要修改密码
  }
  ```
- **成功响应**:
  ```json
  {
    "code": 200,
    "message": "修改成功"
  }
  ```
- **错误响应**:
  ```json
  {
    "code": 404,
    "message": "用户不存在"
  }
  ```
  或
  ```json
  {
    "code": 400,
    "message": "手机号或邮箱已被其他用户使用"
  }
  ```

### 4.5 删除用户账号

- **URL**: `/admin/users/{userId}`
- **方法**: `DELETE`
- **描述**: 管理员删除用户（患者/医生）账号
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **成功响应**:
  ```json
  {
    "code": 200,
    "message": "删除成功"
  }
  ```
- **错误响应**:
  ```json
  {
    "code": 404,
    "message": "用户不存在"
  }
  ```
  或
  ```json
  {
    "code": 403,
    "message": "权限不足，需要管理员权限"
  }
  ```

## 5. 预约相关接口

### 5.1 创建预约

- **URL**: `/appointments`
- **方法**: `POST`
- **描述**: 患者创建预约
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **请求参数**:
  ```json
  {
    "patientId": "P10001",
    "doctorId": "D10001",
    "availableTimeId": "AT10001",
    "description": "牙齿矫正咨询"
  }
  ```
- **成功响应**:
  ```json
  {
    "code": 200,
    "message": "预约成功",
    "data": {
      "appointmentId": "A10001",
      "date": "2023-11-15",
      "startTime": "09:00",
      "endTime": "10:00",
      "doctorName": "李医生",
      "clinicAddress": "北京市朝阳区健康路88号"
    }
  }
  ```
- **错误响应**:
  ```json
  {
    "code": 404,
    "message": "预约时间不存在"
  }
  ```
  或
  ```json
  {
    "code": 400,
    "message": "该时间段已被预约"
  }
  ```

### 5.2 获取预约详情

- **URL**: `/appointments/{appointmentId}`
- **方法**: `GET`
- **描述**: 获取预约详情
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **成功响应**:
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "id": "A10001",
      "date": "2023-11-15",
      "startTime": "09:00",
      "endTime": "10:00",
      "status": "upcoming",
      "doctor": {
        "id": "D10001",
        "name": "李医生",
        "specialization": "口腔正畸科",
        "phone": "***********",
        "email": "<EMAIL>",
        "clinicAddress": "北京市朝阳区健康路88号"
      },
      "patient": {
        "id": "P10001",
        "name": "张三",
        "gender": "男",
        "phone": "***********",
        "email": "<EMAIL>"
      },
      "description": "牙齿矫正咨询"
    }
  }
  ```
- **错误响应**:
  ```json
  {
    "code": 404,
    "message": "预约不存在"
  }
  ```