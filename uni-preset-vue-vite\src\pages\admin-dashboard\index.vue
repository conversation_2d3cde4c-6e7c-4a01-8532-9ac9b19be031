<template>
  <view class="container">
    <view class="options-container">
      <!-- 管理医生账号选项 -->
      <view class="option-card" @tap="navigateToDoctorManagement">
        <view class="option-content">
          <text class="option-title">管理医生账号</text>
          <text class="option-desc">添加、编辑或删除医生账号信息</text>
        </view>
        <view class="option-icon">
          <text class="icon">👨‍⚕️</text>
        </view>
      </view>
      
      <!-- 管理用户账号选项 -->
      <view class="option-card" @tap="navigateToPatientManagement">
        <view class="option-content">
          <text class="option-title">管理用户账号</text>
          <text class="option-desc">查看和管理用户账号信息</text>
        </view>
        <view class="option-icon">
          <text class="icon">👥</text>
        </view>
      </view>
    </view>
    
    <!-- 退出登录按钮 -->
    <view class="logout-container">
      <button class="logout-button" @tap="logout">退出登录</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 管理员信息
const adminInfo = ref({
  name: 'Admin',
  id: 'A001'
});

// 页面加载时
onMounted(() => {
  // 获取管理员信息
  try {
    const admin = uni.getStorageSync('adminInfo');
    if (admin) {
      adminInfo.value = admin;
    }
  } catch (e) {
    console.error('获取管理员信息失败', e);
  }
});

// 跳转到医生账号管理页面
const navigateToDoctorManagement = () => {
  uni.navigateTo({
    url: '/pages/doctor-management/index'
  });
};

// 跳转到用户账号管理页面
const navigateToPatientManagement = () => {
  uni.navigateTo({
    url: '/pages/patient-management/index'
  });
};

// 退出登录方法
const logout = () => {
  uni.showModal({
    title: '确认退出',
    content: '您确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        // 清除登录状态和用户信息
        try {
          uni.removeStorageSync('adminInfo');
          uni.removeStorageSync('token');
          // 可以根据需要清除其他存储的信息
          
          // 跳转到登录页面
          uni.reLaunch({
            url: '/pages/login/index'
          });
        } catch (e) {
          console.error('退出登录失败', e);
          uni.showToast({
            title: '退出失败，请重试',
            icon: 'none'
          });
        }
      }
    }
  });
};
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #a8c0ff;
  background-image: linear-gradient(135deg, #a8c0ff 0%, #8e99f3 100%);
  padding: 60rpx 0 30rpx 0;
  overflow: hidden;
  box-sizing: border-box;
}



.options-container {
  display: flex;
  flex-direction: column;
  padding: 0 30rpx;
  gap: 25rpx;
  flex: 1;
  justify-content: center;
  min-height: 0;
}

.option-card {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 25rpx;
  padding: 25rpx 25rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 3rpx 8rpx rgba(0, 0, 0, 0.1);
  }
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.option-desc {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.option-icon {
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(142, 153, 243, 0.2);
  border-radius: 15rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon {
  font-size: 40rpx;
}

/* 退出登录按钮样式 */
.logout-container {
  padding: 0 30rpx 0;
  flex-shrink: 0;
}

.logout-button {
  background-color: rgba(255, 255, 255, 0.8);
  color: #5a67d8;
  font-size: 28rpx;
  padding: 15rpx 0;
  border-radius: 40rpx;
  border: 1rpx solid rgba(90, 103, 216, 0.3);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  }
}
</style> 