<template>
  <view class="register-container">
    <!-- 注册卡片 -->
    <view class="register-card">
      <view class="register-header">
        <text class="title">账号注册</text>
        <text class="subtitle">创建您的新账号</text>
      </view>
      
      <!-- 提示信息 -->
      <view class="tips-box">
        <text class="tips-icon">💡</text>
        <text class="tips-text">提示：同一手机号可以分别注册一个用户、医生和管理员账户</text>
      </view>
      
      <form>
        <!-- 角色选择 -->
        <view class="form-item role-selection">
          <text class="label">选择角色 <text class="required">*</text></text>
          <view class="role-options">
            <view 
              class="role-option" 
              :class="{ active: formData.role === 'patient' }"
              @tap="selectRole('patient')"
            >
              <text class="role-icon">👤</text>
              <text class="role-name">用户</text>
            </view>
            <view 
              class="role-option" 
              :class="{ active: formData.role === 'doctor' }"
              @tap="selectRole('doctor')"
            >
              <text class="role-icon">👨‍⚕️</text>
              <text class="role-name">医生</text>
            </view>
            <view 
              class="role-option" 
              :class="{ active: formData.role === 'admin' }"
              @tap="selectRole('admin')"
            >
              <text class="role-icon">👮</text>
              <text class="role-name">管理员</text>
            </view>
          </view>
          <text v-if="errors.role" class="error-tip">{{ errors.role }}</text>
        </view>
        
        <!-- 手机号 -->
        <view class="form-item">
          <text class="label">手机号 <text class="required">*</text></text>
          <view class="input-wrapper">
            <input 
              class="input" 
              type="number" 
              placeholder="请输入手机号码" 
              v-model="formData.phone" 
              placeholder-class="placeholder"
              maxlength="11"
            />
            <text class="icon">📱</text>
          </view>
          <text v-if="errors.phone" class="error-tip">{{ errors.phone }}</text>
        </view>
        
        <!-- 邮箱（非必须） -->
        <view class="form-item">
          <text class="label">邮箱</text>
          <view class="input-wrapper">
            <input 
              class="input" 
              type="text" 
              placeholder="请输入邮箱（选填）" 
              v-model="formData.email" 
              placeholder-class="placeholder"
            />
            <text class="icon">✉️</text>
          </view>
          <text v-if="errors.email" class="error-tip">{{ errors.email }}</text>
        </view>
        
        <!-- 密码 -->
        <view class="form-item">
          <text class="label">密码 <text class="required">*</text></text>
          <view class="input-wrapper">
            <input 
              class="input" 
              :type="showPassword ? 'text' : 'password'" 
              placeholder="请设置密码" 
              v-model="formData.password" 
              placeholder-class="placeholder"
            />
            <text class="icon clickable" @tap="togglePasswordVisibility">{{ showPassword ? '👁️' : '🔒' }}</text>
          </view>
          <text v-if="errors.password" class="error-tip">{{ errors.password }}</text>
        </view>
        
        <!-- 确认密码 -->
        <view class="form-item">
          <text class="label">确认密码 <text class="required">*</text></text>
          <view class="input-wrapper">
            <input 
              class="input" 
              :type="showConfirmPassword ? 'text' : 'password'" 
              placeholder="请再次输入密码" 
              v-model="formData.confirmPassword" 
              placeholder-class="placeholder"
            />
            <text class="icon clickable" @tap="toggleConfirmPasswordVisibility">{{ showConfirmPassword ? '👁️' : '🔒' }}</text>
          </view>
          <text v-if="errors.confirmPassword" class="error-tip">{{ errors.confirmPassword }}</text>
        </view>
        
        <!-- 安全问题 -->
        <view class="form-item">
          <text class="label">安全问题 <text class="required">*</text></text>
          <text class="sub-label">您时常最想念的物品或人</text>
          <view class="input-wrapper">
            <input 
              class="input" 
              type="text" 
              placeholder="请输入安全问题答案" 
              v-model="formData.securityAnswer" 
              placeholder-class="placeholder"
            />
            <text class="icon">🔑</text>
          </view>
          <text v-if="errors.securityAnswer" class="error-tip">{{ errors.securityAnswer }}</text>
        </view>
        
        <!-- 管理员安全密码 (仅当角色为管理员时显示) -->
        <view class="form-item" v-if="formData.role === 'admin'">
          <text class="label">管理员安全密码 <text class="required">*</text></text>
          <view class="input-wrapper">
            <input 
              class="input" 
              type="password" 
              placeholder="请输入管理员安全密码" 
              v-model="formData.adminPassword" 
              placeholder-class="placeholder"
            />
            <text class="icon">🔐</text>
          </view>
          <text v-if="errors.adminPassword" class="error-tip">{{ errors.adminPassword }}</text>
        </view>
        
        <!-- 用户协议 -->
        <view class="agreement">
          <checkbox 
            :checked="formData.agreement" 
            @tap="toggleAgreement" 
            class="checkbox" 
            color="#a8c0ff"
          />
          <text>我已阅读并同意</text>
          <text class="link" @tap="showTerms">《用户协议》</text>
          <text>和</text>
          <text class="link" @tap="showPrivacy">《隐私政策》</text>
        </view>
        
        <button 
          class="register-button" 
          @tap="handleRegister" 
          :disabled="!formData.agreement"
          :class="{'button-disabled': !formData.agreement}"
        >注 册</button>
        
        <view class="login-link">
          <text>已有账号？</text>
          <text class="link" @tap="goToLogin">立即登录</text>
        </view>
      </form>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';

// 获取全局API
const { proxy } = getCurrentInstance();
const { $api } = proxy;

// 表单数据
const formData = reactive({
  role: 'patient', // 默认选择用户角色
  phone: '',
  email: '',
  password: '',
  confirmPassword: '',
  securityAnswer: '',
  adminPassword: '',
  agreement: false
});

// 错误信息
const errors = reactive({
  role: '',
  phone: '',
  email: '',
  password: '',
  confirmPassword: '',
  securityAnswer: '',
  adminPassword: ''
});

// 密码可见性
const showPassword = ref(false);
const showConfirmPassword = ref(false);

// 选择角色
const selectRole = (role) => {
  formData.role = role;
  errors.role = '';
};

// 切换密码可见性
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
};

// 切换确认密码可见性
const toggleConfirmPasswordVisibility = () => {
  showConfirmPassword.value = !showConfirmPassword.value;
};

// 切换协议同意状态
const toggleAgreement = () => {
  formData.agreement = !formData.agreement;
};

// 验证角色
const validateRole = () => {
  if (!formData.role) {
    errors.role = '请选择注册角色';
    return false;
  } else {
    errors.role = '';
    return true;
  }
};

// 验证手机号
const validatePhone = () => {
  const phoneReg = /^1[3-9]\d{9}$/;
  if (!formData.phone) {
    errors.phone = '请输入手机号';
    return false;
  } else if (!phoneReg.test(formData.phone)) {
    errors.phone = '手机号格式不正确';
    return false;
  } else {
    errors.phone = '';
    return true;
  }
};

// 验证邮箱
const validateEmail = () => {
  if (!formData.email) {
    errors.email = '';
    return true;  // 邮箱非必填
  }
  
  const emailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
  if (!emailReg.test(formData.email)) {
    errors.email = '邮箱格式不正确';
    return false;
  } else {
    errors.email = '';
    return true;
  }
};

// 验证密码
const validatePassword = () => {
  if (!formData.password) {
    errors.password = '请设置密码';
    return false;
  } else if (formData.password.length < 6) {
    errors.password = '密码长度不能少于6位';
    return false;
  } else {
    errors.password = '';
    return true;
  }
};

// 验证确认密码
const validateConfirmPassword = () => {
  if (!formData.confirmPassword) {
    errors.confirmPassword = '请再次输入密码';
    return false;
  } else if (formData.confirmPassword !== formData.password) {
    errors.confirmPassword = '两次输入的密码不一致';
    return false;
  } else {
    errors.confirmPassword = '';
    return true;
  }
};

// 验证安全问题
const validateSecurityAnswer = () => {
  if (!formData.securityAnswer) {
    errors.securityAnswer = '请输入安全问题答案';
    return false;
  } else {
    errors.securityAnswer = '';
    return true;
  }
};

// 验证管理员安全密码
const validateAdminPassword = () => {
  if (formData.role === 'admin') {
    if (!formData.adminPassword) {
      errors.adminPassword = '请输入管理员安全密码';
      return false;
    } else if (formData.adminPassword !== '1366') {
      errors.adminPassword = '管理员安全密码不正确';
      return false;
    } else {
      errors.adminPassword = '';
      return true;
    }
  }
  return true;
};

// 验证表单
const validateForm = () => {
  const roleValid = validateRole();
  const phoneValid = validatePhone();
  const emailValid = validateEmail();
  const passwordValid = validatePassword();
  const confirmPasswordValid = validateConfirmPassword();
  const securityAnswerValid = validateSecurityAnswer();
  const adminPasswordValid = validateAdminPassword();
  
  return roleValid && phoneValid && emailValid && passwordValid && confirmPasswordValid && securityAnswerValid && adminPasswordValid;
};

// 注册处理
const handleRegister = async () => {
  if (!formData.agreement) {
    uni.showToast({
      title: '请先同意用户协议和隐私政策',
      icon: 'none'
    });
    return;
  }
  
  if (!validateForm()) {
    return;
  }
  
  // 额外验证管理员安全密码
  if (formData.role === 'admin' && formData.adminPassword !== '1366') {
    uni.showToast({
      title: '管理员安全密码不正确',
      icon: 'none'
    });
    return;
  }
  
  // 准备注册数据，确保字段名称与API文档一致
  const registerData = {
    userType: formData.role,  // 用户类型：患者、医生或管理员
    name: formData.phone.substring(0, 3) + '用户',  // 用户姓名
    gender: '未知',  // 性别
    phone: formData.phone,  // 手机号码
    email: formData.email || '',  // 电子邮箱
    password: formData.password,  // 密码
    securityQuestion: '您时常最想念的物品或人',  // 安全问题
    securityAnswer: formData.securityAnswer  // 安全问题答案
  };
  
  console.log('准备注册数据:', JSON.stringify(registerData, null, 2));
  
  try {
    uni.showLoading({
      title: '注册中...'
    });
    
    // 调用注册API
    console.log('开始调用注册API...');
    const result = await $api.auth.register(registerData);
    console.log('注册API调用成功:', JSON.stringify(result, null, 2));
    
    uni.hideLoading();
    uni.showToast({
      title: '注册成功',
      icon: 'success'
    });
    
    // 清空表单数据
    formData.phone = '';
    formData.email = '';
    formData.password = '';
    formData.confirmPassword = '';
    formData.securityAnswer = '';
    formData.adminPassword = '';
    
    // 注册成功后跳转到登录页面
    setTimeout(() => {
      uni.navigateTo({
        url: '/pages/login/index?account=' + registerData.phone
      });
    }, 1500);
  } catch (error) {
    uni.hideLoading();
    
    // 显示错误信息
    let errorMessage = '注册失败，请重试';
    if (error.message) {
      errorMessage = error.message;
    } else if (error.errMsg) {
      errorMessage = error.errMsg;
    }
    
    console.error('注册失败:', JSON.stringify(error, null, 2));
    
    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 3000
    });
  }
};

// 显示用户协议
const showTerms = () => {
  uni.showModal({
    title: '用户协议',
    content: '这是用户协议内容...',
    showCancel: false
  });
};

// 显示隐私政策
const showPrivacy = () => {
  uni.showModal({
    title: '隐私政策',
    content: '这是隐私政策内容...',
    showCancel: false
  });
};

// 去登录页面
const goToLogin = () => {
  uni.navigateTo({
    url: '/pages/login/index'
  });
};
</script>

<style lang="scss">
.register-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #a8c0ff;
  padding: 30rpx;
}

.register-card {
  width: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 30rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.05);
}

.register-header {
  text-align: center;
  margin-bottom: 30rpx;
  
  .title {
    font-size: 44rpx;
    font-weight: 600;
    color: #333;
    display: block;
    margin-bottom: 10rpx;
  }
  
  .subtitle {
    font-size: 28rpx;
    color: #666;
  }
}

.tips-box {
  background-color: #f0f0f0;
  border-radius: 16rpx;
  padding: 15rpx 20rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #555;

  .tips-icon {
    font-size: 36rpx;
    margin-right: 10rpx;
  }
}

.form-item {
  margin-bottom: 25rpx;
  
  .label {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 8rpx;
    display: block;
    
    .required {
      color: #ff4d4f;
    }
  }
  
  .sub-label {
    font-size: 24rpx;
    color: #999;
    margin-bottom: 8rpx;
    display: block;
  }
  
  .input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    
    .input {
      flex: 1;
      height: 90rpx;
      border: 1px solid #e0e0e0;
      border-radius: 16rpx;
      padding: 0 80rpx 0 30rpx;
      font-size: 28rpx;
      background-color: #fff;
      
      &:focus {
        border-color: #a8c0ff;
      }
    }
    
    .icon {
      position: absolute;
      right: 30rpx;
      font-size: 36rpx;
      color: #999;
      
      &.clickable {
        cursor: pointer;
      }
    }
    
    .placeholder {
      color: #999;
    }
  }
  
  .error-tip {
    font-size: 24rpx;
    color: #ff4d4f;
    margin-top: 8rpx;
    display: block;
  }
}

.role-selection {
  margin-bottom: 30rpx;
}

.role-options {
  display: flex;
  justify-content: space-between;
  margin-top: 10rpx;
}

.role-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx 10rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 16rpx;
  margin: 0 10rpx;
  transition: all 0.3s;
  
  &:first-child {
    margin-left: 0;
  }
  
  &:last-child {
    margin-right: 0;
  }
  
  .role-icon {
    font-size: 40rpx;
    margin-bottom: 10rpx;
  }
  
  .role-name {
    font-size: 26rpx;
    color: #333;
  }
  
  &.active {
    border-color: #a8c0ff;
    background-color: rgba(168, 192, 255, 0.1);
    
    .role-name {
      color: #8e99f3;
      font-weight: 500;
    }
  }
}

.agreement {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 30rpx;
  font-size: 26rpx;
  color: #666;
  
  .checkbox {
    transform: scale(0.8);
    margin-right: 8rpx;
  }
  
  .link {
    color: #8e99f3;
    margin: 0 4rpx;
  }
}

.register-button {
  width: 100%;
  height: 90rpx;
  background-color: #a8c0ff;
  color: #fff;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  
  &:active {
    background-color: #97b1f8;
  }
  
  &.button-disabled {
    background-color: #cccccc;
    color: #ffffff;
  }
}

.login-link {
  text-align: center;
  font-size: 26rpx;
  color: #666;
  
  .link {
    color: #8e99f3;
    margin-left: 10rpx;
  }
}
</style> 