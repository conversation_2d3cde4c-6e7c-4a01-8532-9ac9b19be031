<script>
import { isTokenExpired, clearUserInfo } from '@/utils/storage'

export default {
  onLaunch: function () {
    console.log('App Launch')
    // 检查Token是否过期
    this.checkTokenStatus()
  },
  onShow: function () {
    console.log('App Show')
  },
  onHide: function () {
    console.log('App Hide')
  },
  methods: {
    // 检查Token状态
    checkTokenStatus() {
      try {
        if (isTokenExpired()) {
          // Token已过期，清除用户信息
          clearUserInfo()
          
          // 获取当前页面路径
          const pages = getCurrentPages()
          const currentPage = pages[pages.length - 1]
          const currentRoute = currentPage ? currentPage.route : ''
          
          // 如果当前页需要登录，则重定向到登录页
          if (currentRoute && 
              !['pages/login/index', 'pages/register/index', 'pages/forgot-password/index'].includes(currentRoute)) {
            uni.showToast({
              title: '登录已过期，请重新登录',
              icon: 'none',
              duration: 1500,
              complete: () => {
                setTimeout(() => {
                  uni.reLaunch({
                    url: '/pages/login/index'
                  })
                }, 1500)
              }
            })
          }
        }
      } catch (error) {
        console.error('检查Token状态失败', error)
      }
    }
  }
}
</script>

<style>
/*每个页面公共css */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,
    Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei',
    sans-serif;
  -webkit-font-smoothing: antialiased;
}
</style>
