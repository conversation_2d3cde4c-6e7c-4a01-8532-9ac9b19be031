<template>
  <view class="dental-health-card" v-if="record">
    <view class="card-header">
      <text class="card-title">口腔健康状况</text>
      <text class="check-date">{{ formatDate }}</text>
    </view>
    
    <view class="health-score-section">
      <view class="score-circle" :style="{ 
        background: `conic-gradient(${getScoreColor} ${scorePercentage}%, #f0f0f0 0)` 
      }">
        <view class="score-inner">
          <text class="score-text">{{ record.health_score }}</text>
          <text class="score-label">健康分</text>
        </view>
      </view>
      
      <view class="score-details">
        <view class="detail-item">
          <text class="detail-label">龋齿比例:</text>
          <text class="detail-value">{{ record.caries_ratio }}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">空洞比例:</text>
          <text class="detail-value">{{ record.cavity_ratio }}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">牙菌斑比例:</text>
          <text class="detail-value">{{ record.plaque_ratio }}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">清洁度评分:</text>
          <text class="detail-value">{{ record.cleanliness_score }}</text>
        </view>
      </view>
    </view>
    
    <view class="card-footer">
      <text class="view-details" @tap="viewDetails">查看详情</text>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue';

// 接收传入的记录数据
const props = defineProps({
  record: {
    type: Object,
    default: () => null
  }
});

// 格式化日期
const formatDate = computed(() => {
  if (!props.record || !props.record.created_at) return '';
  return `检查日期: ${props.record.created_at.split(' ')[0]}`;
});

// 计算健康评分百分比
const scorePercentage = computed(() => {
  if (!props.record || !props.record.health_score) return 0;
  return (props.record.health_score / 100) * 100;
});

// 根据评分获取颜色
const getScoreColor = computed(() => {
  const score = props.record?.health_score || 0;
  if (score >= 85) return '#4cd964'; // 好
  if (score >= 70) return '#ffcc00'; // 中
  return '#ff3b30'; // 差
});

// 查看详情
const viewDetails = () => {
  if (!props.record) return;
  uni.navigateTo({
    url: `/pages/dental-health-detail/index?id=${props.record.id}`
  });
};

// 暴露方法
defineExpose({
  viewDetails
});
</script>

<style lang="scss">
.dental-health-card {
  width: 94%;
  margin: 0 auto 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.card-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.check-date {
  font-size: 22rpx;
  color: #999;
}

.health-score-section {
  display: flex;
  align-items: center;
  margin: 15rpx 0;
}

.score-circle {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.score-inner {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.score-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  line-height: 1;
}

.score-label {
  font-size: 20rpx;
  color: #999;
  margin-top: 4rpx;
}

.score-details {
  flex: 1;
  margin-left: 20rpx;
  overflow: hidden;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.detail-label {
  font-size: 24rpx;
  color: #666;
  width: 140rpx;
  flex-shrink: 0;
}

.detail-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  text-align: right;
  padding-right: 10rpx;
}

.card-footer {
  margin-top: 15rpx;
  display: flex;
  justify-content: flex-end;
}

.view-details {
  font-size: 24rpx;
  color: #4a90e2;
  padding: 4rpx 8rpx;
}
</style> 